import contextlib
import io
import logging
import os

from detectron2.data import DatasetCatalog, MetadataCatalog

from .coco import custom_load_coco_json
from .openimages_v6_category_image_count import OPENIMAGES_v6_CATEGORY_IMAGE_COUNT


def register_oid_instances(name, metadata, json_file, image_root):
    """
    Register a dataset in COCO's json annotation format for
    instance detection, instance segmentation and keypoint detection.
    (i.e., Type 1 and 2 in http://cocodataset.org/#format-data.
    `instances*.json` and `person_keypoints*.json` in the dataset).

    This is an example of how to register a new dataset.
    You can do something similar to this function, to register new datasets.

    Args:
        name (str): the name that identifies a dataset, e.g. "coco_2014_train".
        metadata (dict): extra metadata associated with this dataset.  You can
            leave it as an empty dict.
        json_file (str): path to the json instance annotation file.
        image_root (str or path-like): directory which contains all the images.
    """
    assert isinstance(name, str), name
    assert isinstance(json_file, (str, os.PathLike)), json_file
    assert isinstance(image_root, (str, os.PathLike)), image_root
    # 1. register a function which returns dicts
    DatasetCatalog.register(name, lambda: custom_load_coco_json(json_file, image_root, name))

    # 2. Optionally, add metadata about this dataset,
    # since they might be useful in evaluation, visualization or logging
    MetadataCatalog.get(name).set(
        json_file=json_file, image_root=image_root, evaluator_type="oid", **metadata
    )


OPENIMAGES_2019_CATEGORIES = [
    {"id": 1, "name": "Infant bed", "freebase_id": "/m/061hd_"},
    {"id": 2, "name": "Rose", "freebase_id": "/m/06m11"},
    {"id": 3, "name": "Flag", "freebase_id": "/m/03120"},
    {"id": 4, "name": "Flashlight", "freebase_id": "/m/01kb5b"},
    {"id": 5, "name": "Sea turtle", "freebase_id": "/m/0120dh"},
    {"id": 6, "name": "Camera", "freebase_id": "/m/0dv5r"},
    {"id": 7, "name": "Animal", "freebase_id": "/m/0jbk"},
    {"id": 8, "name": "Glove", "freebase_id": "/m/0174n1"},
    {"id": 9, "name": "Crocodile", "freebase_id": "/m/09f_2"},
    {"id": 10, "name": "Cattle", "freebase_id": "/m/01xq0k1"},
    {"id": 11, "name": "House", "freebase_id": "/m/03jm5"},
    {"id": 12, "name": "Guacamole", "freebase_id": "/m/02g30s"},
    {"id": 13, "name": "Penguin", "freebase_id": "/m/05z6w"},
    {"id": 14, "name": "Vehicle registration plate", "freebase_id": "/m/01jfm_"},
    {"id": 15, "name": "Bench", "freebase_id": "/m/076lb9"},
    {"id": 16, "name": "Ladybug", "freebase_id": "/m/0gj37"},
    {"id": 17, "name": "Human nose", "freebase_id": "/m/0k0pj"},
    {"id": 18, "name": "Watermelon", "freebase_id": "/m/0kpqd"},
    {"id": 19, "name": "Flute", "freebase_id": "/m/0l14j_"},
    {"id": 20, "name": "Butterfly", "freebase_id": "/m/0cyf8"},
    {"id": 21, "name": "Washing machine", "freebase_id": "/m/0174k2"},
    {"id": 22, "name": "Raccoon", "freebase_id": "/m/0dq75"},
    {"id": 23, "name": "Segway", "freebase_id": "/m/076bq"},
    {"id": 24, "name": "Taco", "freebase_id": "/m/07crc"},
    {"id": 25, "name": "Jellyfish", "freebase_id": "/m/0d8zb"},
    {"id": 26, "name": "Cake", "freebase_id": "/m/0fszt"},
    {"id": 27, "name": "Pen", "freebase_id": "/m/0k1tl"},
    {"id": 28, "name": "Cannon", "freebase_id": "/m/020kz"},
    {"id": 29, "name": "Bread", "freebase_id": "/m/09728"},
    {"id": 30, "name": "Tree", "freebase_id": "/m/07j7r"},
    {"id": 31, "name": "Shellfish", "freebase_id": "/m/0fbdv"},
    {"id": 32, "name": "Bed", "freebase_id": "/m/03ssj5"},
    {"id": 33, "name": "Hamster", "freebase_id": "/m/03qrc"},
    {"id": 34, "name": "Hat", "freebase_id": "/m/02dl1y"},
    {"id": 35, "name": "Toaster", "freebase_id": "/m/01k6s3"},
    {"id": 36, "name": "Sombrero", "freebase_id": "/m/02jfl0"},
    {"id": 37, "name": "Tiara", "freebase_id": "/m/01krhy"},
    {"id": 38, "name": "Bowl", "freebase_id": "/m/04kkgm"},
    {"id": 39, "name": "Dragonfly", "freebase_id": "/m/0ft9s"},
    {"id": 40, "name": "Moths and butterflies", "freebase_id": "/m/0d_2m"},
    {"id": 41, "name": "Antelope", "freebase_id": "/m/0czz2"},
    {"id": 42, "name": "Vegetable", "freebase_id": "/m/0f4s2w"},
    {"id": 43, "name": "Torch", "freebase_id": "/m/07dd4"},
    {"id": 44, "name": "Building", "freebase_id": "/m/0cgh4"},
    {"id": 45, "name": "Power plugs and sockets", "freebase_id": "/m/03bbps"},
    {"id": 46, "name": "Blender", "freebase_id": "/m/02pjr4"},
    {"id": 47, "name": "Billiard table", "freebase_id": "/m/04p0qw"},
    {"id": 48, "name": "Cutting board", "freebase_id": "/m/02pdsw"},
    {"id": 49, "name": "Bronze sculpture", "freebase_id": "/m/01yx86"},
    {"id": 50, "name": "Turtle", "freebase_id": "/m/09dzg"},
    {"id": 51, "name": "Broccoli", "freebase_id": "/m/0hkxq"},
    {"id": 52, "name": "Tiger", "freebase_id": "/m/07dm6"},
    {"id": 53, "name": "Mirror", "freebase_id": "/m/054_l"},
    {"id": 54, "name": "Bear", "freebase_id": "/m/01dws"},
    {"id": 55, "name": "Zucchini", "freebase_id": "/m/027pcv"},
    {"id": 56, "name": "Dress", "freebase_id": "/m/01d40f"},
    {"id": 57, "name": "Volleyball", "freebase_id": "/m/02rgn06"},
    {"id": 58, "name": "Guitar", "freebase_id": "/m/0342h"},
    {"id": 59, "name": "Reptile", "freebase_id": "/m/06bt6"},
    {"id": 60, "name": "Golf cart", "freebase_id": "/m/0323sq"},
    {"id": 61, "name": "Tart", "freebase_id": "/m/02zvsm"},
    {"id": 62, "name": "Fedora", "freebase_id": "/m/02fq_6"},
    {"id": 63, "name": "Carnivore", "freebase_id": "/m/01lrl"},
    {"id": 64, "name": "Car", "freebase_id": "/m/0k4j"},
    {"id": 65, "name": "Lighthouse", "freebase_id": "/m/04h7h"},
    {"id": 66, "name": "Coffeemaker", "freebase_id": "/m/07xyvk"},
    {"id": 67, "name": "Food processor", "freebase_id": "/m/03y6mg"},
    {"id": 68, "name": "Truck", "freebase_id": "/m/07r04"},
    {"id": 69, "name": "Bookcase", "freebase_id": "/m/03__z0"},
    {"id": 70, "name": "Surfboard", "freebase_id": "/m/019w40"},
    {"id": 71, "name": "Footwear", "freebase_id": "/m/09j5n"},
    {"id": 72, "name": "Bench", "freebase_id": "/m/0cvnqh"},
    {"id": 73, "name": "Necklace", "freebase_id": "/m/01llwg"},
    {"id": 74, "name": "Flower", "freebase_id": "/m/0c9ph5"},
    {"id": 75, "name": "Radish", "freebase_id": "/m/015x5n"},
    {"id": 76, "name": "Marine mammal", "freebase_id": "/m/0gd2v"},
    {"id": 77, "name": "Frying pan", "freebase_id": "/m/04v6l4"},
    {"id": 78, "name": "Tap", "freebase_id": "/m/02jz0l"},
    {"id": 79, "name": "Peach", "freebase_id": "/m/0dj6p"},
    {"id": 80, "name": "Knife", "freebase_id": "/m/04ctx"},
    {"id": 81, "name": "Handbag", "freebase_id": "/m/080hkjn"},
    {"id": 82, "name": "Laptop", "freebase_id": "/m/01c648"},
    {"id": 83, "name": "Tent", "freebase_id": "/m/01j61q"},
    {"id": 84, "name": "Ambulance", "freebase_id": "/m/012n7d"},
    {"id": 85, "name": "Christmas tree", "freebase_id": "/m/025nd"},
    {"id": 86, "name": "Eagle", "freebase_id": "/m/09csl"},
    {"id": 87, "name": "Limousine", "freebase_id": "/m/01lcw4"},
    {"id": 88, "name": "Kitchen & dining room table", "freebase_id": "/m/0h8n5zk"},
    {"id": 89, "name": "Polar bear", "freebase_id": "/m/0633h"},
    {"id": 90, "name": "Tower", "freebase_id": "/m/01fdzj"},
    {"id": 91, "name": "Football", "freebase_id": "/m/01226z"},
    {"id": 92, "name": "Willow", "freebase_id": "/m/0mw_6"},
    {"id": 93, "name": "Human head", "freebase_id": "/m/04hgtk"},
    {"id": 94, "name": "Stop sign", "freebase_id": "/m/02pv19"},
    {"id": 95, "name": "Banana", "freebase_id": "/m/09qck"},
    {"id": 96, "name": "Mixer", "freebase_id": "/m/063rgb"},
    {"id": 97, "name": "Binoculars", "freebase_id": "/m/0lt4_"},
    {"id": 98, "name": "Dessert", "freebase_id": "/m/0270h"},
    {"id": 99, "name": "Bee", "freebase_id": "/m/01h3n"},
    {"id": 100, "name": "Chair", "freebase_id": "/m/01mzpv"},
    {"id": 101, "name": "Wood-burning stove", "freebase_id": "/m/04169hn"},
    {"id": 102, "name": "Flowerpot", "freebase_id": "/m/0fm3zh"},
    {"id": 103, "name": "Beaker", "freebase_id": "/m/0d20w4"},
    {"id": 104, "name": "Oyster", "freebase_id": "/m/0_cp5"},
    {"id": 105, "name": "Woodpecker", "freebase_id": "/m/01dy8n"},
    {"id": 106, "name": "Harp", "freebase_id": "/m/03m5k"},
    {"id": 107, "name": "Bathtub", "freebase_id": "/m/03dnzn"},
    {"id": 108, "name": "Wall clock", "freebase_id": "/m/0h8mzrc"},
    {"id": 109, "name": "Sports uniform", "freebase_id": "/m/0h8mhzd"},
    {"id": 110, "name": "Rhinoceros", "freebase_id": "/m/03d443"},
    {"id": 111, "name": "Beehive", "freebase_id": "/m/01gllr"},
    {"id": 112, "name": "Cupboard", "freebase_id": "/m/0642b4"},
    {"id": 113, "name": "Chicken", "freebase_id": "/m/09b5t"},
    {"id": 114, "name": "Man", "freebase_id": "/m/04yx4"},
    {"id": 115, "name": "Blue jay", "freebase_id": "/m/01f8m5"},
    {"id": 116, "name": "Cucumber", "freebase_id": "/m/015x4r"},
    {"id": 117, "name": "Balloon", "freebase_id": "/m/01j51"},
    {"id": 118, "name": "Kite", "freebase_id": "/m/02zt3"},
    {"id": 119, "name": "Fireplace", "freebase_id": "/m/03tw93"},
    {"id": 120, "name": "Lantern", "freebase_id": "/m/01jfsr"},
    {"id": 121, "name": "Missile", "freebase_id": "/m/04ylt"},
    {"id": 122, "name": "Book", "freebase_id": "/m/0bt_c3"},
    {"id": 123, "name": "Spoon", "freebase_id": "/m/0cmx8"},
    {"id": 124, "name": "Grapefruit", "freebase_id": "/m/0hqkz"},
    {"id": 125, "name": "Squirrel", "freebase_id": "/m/071qp"},
    {"id": 126, "name": "Orange", "freebase_id": "/m/0cyhj_"},
    {"id": 127, "name": "Coat", "freebase_id": "/m/01xygc"},
    {"id": 128, "name": "Punching bag", "freebase_id": "/m/0420v5"},
    {"id": 129, "name": "Zebra", "freebase_id": "/m/0898b"},
    {"id": 130, "name": "Billboard", "freebase_id": "/m/01knjb"},
    {"id": 131, "name": "Bicycle", "freebase_id": "/m/0199g"},
    {"id": 132, "name": "Door handle", "freebase_id": "/m/03c7gz"},
    {"id": 133, "name": "Mechanical fan", "freebase_id": "/m/02x984l"},
    {"id": 134, "name": "Ring binder", "freebase_id": "/m/04zwwv"},
    {"id": 135, "name": "Table", "freebase_id": "/m/04bcr3"},
    {"id": 136, "name": "Parrot", "freebase_id": "/m/0gv1x"},
    {"id": 137, "name": "Sock", "freebase_id": "/m/01nq26"},
    {"id": 138, "name": "Vase", "freebase_id": "/m/02s195"},
    {"id": 139, "name": "Weapon", "freebase_id": "/m/083kb"},
    {"id": 140, "name": "Shotgun", "freebase_id": "/m/06nrc"},
    {"id": 141, "name": "Glasses", "freebase_id": "/m/0jyfg"},
    {"id": 142, "name": "Seahorse", "freebase_id": "/m/0nybt"},
    {"id": 143, "name": "Belt", "freebase_id": "/m/0176mf"},
    {"id": 144, "name": "Watercraft", "freebase_id": "/m/01rzcn"},
    {"id": 145, "name": "Window", "freebase_id": "/m/0d4v4"},
    {"id": 146, "name": "Giraffe", "freebase_id": "/m/03bk1"},
    {"id": 147, "name": "Lion", "freebase_id": "/m/096mb"},
    {"id": 148, "name": "Tire", "freebase_id": "/m/0h9mv"},
    {"id": 149, "name": "Vehicle", "freebase_id": "/m/07yv9"},
    {"id": 150, "name": "Canoe", "freebase_id": "/m/0ph39"},
    {"id": 151, "name": "Tie", "freebase_id": "/m/01rkbr"},
    {"id": 152, "name": "Shelf", "freebase_id": "/m/0gjbg72"},
    {"id": 153, "name": "Picture frame", "freebase_id": "/m/06z37_"},
    {"id": 154, "name": "Printer", "freebase_id": "/m/01m4t"},
    {"id": 155, "name": "Human leg", "freebase_id": "/m/035r7c"},
    {"id": 156, "name": "Boat", "freebase_id": "/m/019jd"},
    {"id": 157, "name": "Slow cooker", "freebase_id": "/m/02tsc9"},
    {"id": 158, "name": "Croissant", "freebase_id": "/m/015wgc"},
    {"id": 159, "name": "Candle", "freebase_id": "/m/0c06p"},
    {"id": 160, "name": "Pancake", "freebase_id": "/m/01dwwc"},
    {"id": 161, "name": "Pillow", "freebase_id": "/m/034c16"},
    {"id": 162, "name": "Coin", "freebase_id": "/m/0242l"},
    {"id": 163, "name": "Stretcher", "freebase_id": "/m/02lbcq"},
    {"id": 164, "name": "Sandal", "freebase_id": "/m/03nfch"},
    {"id": 165, "name": "Woman", "freebase_id": "/m/03bt1vf"},
    {"id": 166, "name": "Stairs", "freebase_id": "/m/01lynh"},
    {"id": 167, "name": "Harpsichord", "freebase_id": "/m/03q5t"},
    {"id": 168, "name": "Stool", "freebase_id": "/m/0fqt361"},
    {"id": 169, "name": "Bus", "freebase_id": "/m/01bjv"},
    {"id": 170, "name": "Suitcase", "freebase_id": "/m/01s55n"},
    {"id": 171, "name": "Human mouth", "freebase_id": "/m/0283dt1"},
    {"id": 172, "name": "Juice", "freebase_id": "/m/01z1kdw"},
    {"id": 173, "name": "Skull", "freebase_id": "/m/016m2d"},
    {"id": 174, "name": "Door", "freebase_id": "/m/02dgv"},
    {"id": 175, "name": "Violin", "freebase_id": "/m/07y_7"},
    {"id": 176, "name": "Chopsticks", "freebase_id": "/m/01_5g"},
    {"id": 177, "name": "Digital clock", "freebase_id": "/m/06_72j"},
    {"id": 178, "name": "Sunflower", "freebase_id": "/m/0ftb8"},
    {"id": 179, "name": "Leopard", "freebase_id": "/m/0c29q"},
    {"id": 180, "name": "Bell pepper", "freebase_id": "/m/0jg57"},
    {"id": 181, "name": "Harbor seal", "freebase_id": "/m/02l8p9"},
    {"id": 182, "name": "Snake", "freebase_id": "/m/078jl"},
    {"id": 183, "name": "Sewing machine", "freebase_id": "/m/0llzx"},
    {"id": 184, "name": "Goose", "freebase_id": "/m/0dbvp"},
    {"id": 185, "name": "Helicopter", "freebase_id": "/m/09ct_"},
    {"id": 186, "name": "Seat belt", "freebase_id": "/m/0dkzw"},
    {"id": 187, "name": "Coffee cup", "freebase_id": "/m/02p5f1q"},
    {"id": 188, "name": "Microwave oven", "freebase_id": "/m/0fx9l"},
    {"id": 189, "name": "Hot dog", "freebase_id": "/m/01b9xk"},
    {"id": 190, "name": "Countertop", "freebase_id": "/m/0b3fp9"},
    {"id": 191, "name": "Serving tray", "freebase_id": "/m/0h8n27j"},
    {"id": 192, "name": "Dog bed", "freebase_id": "/m/0h8n6f9"},
    {"id": 193, "name": "Beer", "freebase_id": "/m/01599"},
    {"id": 194, "name": "Sunglasses", "freebase_id": "/m/017ftj"},
    {"id": 195, "name": "Golf ball", "freebase_id": "/m/044r5d"},
    {"id": 196, "name": "Waffle", "freebase_id": "/m/01dwsz"},
    {"id": 197, "name": "Palm tree", "freebase_id": "/m/0cdl1"},
    {"id": 198, "name": "Trumpet", "freebase_id": "/m/07gql"},
    {"id": 199, "name": "Ruler", "freebase_id": "/m/0hdln"},
    {"id": 200, "name": "Helmet", "freebase_id": "/m/0zvk5"},
    {"id": 201, "name": "Ladder", "freebase_id": "/m/012w5l"},
    {"id": 202, "name": "Office building", "freebase_id": "/m/021sj1"},
    {"id": 203, "name": "Tablet computer", "freebase_id": "/m/0bh9flk"},
    {"id": 204, "name": "Toilet paper", "freebase_id": "/m/09gtd"},
    {"id": 205, "name": "Pomegranate", "freebase_id": "/m/0jwn_"},
    {"id": 206, "name": "Skirt", "freebase_id": "/m/02wv6h6"},
    {"id": 207, "name": "Gas stove", "freebase_id": "/m/02wv84t"},
    {"id": 208, "name": "Cookie", "freebase_id": "/m/021mn"},
    {"id": 209, "name": "Cart", "freebase_id": "/m/018p4k"},
    {"id": 210, "name": "Raven", "freebase_id": "/m/06j2d"},
    {"id": 211, "name": "Egg", "freebase_id": "/m/033cnk"},
    {"id": 212, "name": "Burrito", "freebase_id": "/m/01j3zr"},
    {"id": 213, "name": "Goat", "freebase_id": "/m/03fwl"},
    {"id": 214, "name": "Kitchen knife", "freebase_id": "/m/058qzx"},
    {"id": 215, "name": "Skateboard", "freebase_id": "/m/06_fw"},
    {"id": 216, "name": "Salt and pepper shakers", "freebase_id": "/m/02x8cch"},
    {"id": 217, "name": "Lynx", "freebase_id": "/m/04g2r"},
    {"id": 218, "name": "Boot", "freebase_id": "/m/01b638"},
    {"id": 219, "name": "Platter", "freebase_id": "/m/099ssp"},
    {"id": 220, "name": "Ski", "freebase_id": "/m/071p9"},
    {"id": 221, "name": "Swimwear", "freebase_id": "/m/01gkx_"},
    {"id": 222, "name": "Swimming pool", "freebase_id": "/m/0b_rs"},
    {"id": 223, "name": "Drinking straw", "freebase_id": "/m/03v5tg"},
    {"id": 224, "name": "Wrench", "freebase_id": "/m/01j5ks"},
    {"id": 225, "name": "Drum", "freebase_id": "/m/026t6"},
    {"id": 226, "name": "Ant", "freebase_id": "/m/0_k2"},
    {"id": 227, "name": "Human ear", "freebase_id": "/m/039xj_"},
    {"id": 228, "name": "Headphones", "freebase_id": "/m/01b7fy"},
    {"id": 229, "name": "Fountain", "freebase_id": "/m/0220r2"},
    {"id": 230, "name": "Bird", "freebase_id": "/m/015p6"},
    {"id": 231, "name": "Jeans", "freebase_id": "/m/0fly7"},
    {"id": 232, "name": "Television", "freebase_id": "/m/07c52"},
    {"id": 233, "name": "Crab", "freebase_id": "/m/0n28_"},
    {"id": 234, "name": "Microphone", "freebase_id": "/m/0hg7b"},
    {"id": 235, "name": "Home appliance", "freebase_id": "/m/019dx1"},
    {"id": 236, "name": "Snowplow", "freebase_id": "/m/04vv5k"},
    {"id": 237, "name": "Beetle", "freebase_id": "/m/020jm"},
    {"id": 238, "name": "Artichoke", "freebase_id": "/m/047v4b"},
    {"id": 239, "name": "Jet ski", "freebase_id": "/m/01xs3r"},
    {"id": 240, "name": "Stationary bicycle", "freebase_id": "/m/03kt2w"},
    {"id": 241, "name": "Human hair", "freebase_id": "/m/03q69"},
    {"id": 242, "name": "Brown bear", "freebase_id": "/m/01dxs"},
    {"id": 243, "name": "Starfish", "freebase_id": "/m/01h8tj"},
    {"id": 244, "name": "Fork", "freebase_id": "/m/0dt3t"},
    {"id": 245, "name": "Lobster", "freebase_id": "/m/0cjq5"},
    {"id": 246, "name": "Corded phone", "freebase_id": "/m/0h8lkj8"},
    {"id": 247, "name": "Drink", "freebase_id": "/m/0271t"},
    {"id": 248, "name": "Saucer", "freebase_id": "/m/03q5c7"},
    {"id": 249, "name": "Carrot", "freebase_id": "/m/0fj52s"},
    {"id": 250, "name": "Insect", "freebase_id": "/m/03vt0"},
    {"id": 251, "name": "Clock", "freebase_id": "/m/01x3z"},
    {"id": 252, "name": "Castle", "freebase_id": "/m/0d5gx"},
    {"id": 253, "name": "Tennis racket", "freebase_id": "/m/0h8my_4"},
    {"id": 254, "name": "Ceiling fan", "freebase_id": "/m/03ldnb"},
    {"id": 255, "name": "Asparagus", "freebase_id": "/m/0cjs7"},
    {"id": 256, "name": "Jaguar", "freebase_id": "/m/0449p"},
    {"id": 257, "name": "Musical instrument", "freebase_id": "/m/04szw"},
    {"id": 258, "name": "Train", "freebase_id": "/m/07jdr"},
    {"id": 259, "name": "Cat", "freebase_id": "/m/01yrx"},
    {"id": 260, "name": "Rifle", "freebase_id": "/m/06c54"},
    {"id": 261, "name": "Dumbbell", "freebase_id": "/m/04h8sr"},
    {"id": 262, "name": "Mobile phone", "freebase_id": "/m/050k8"},
    {"id": 263, "name": "Taxi", "freebase_id": "/m/0pg52"},
    {"id": 264, "name": "Shower", "freebase_id": "/m/02f9f_"},
    {"id": 265, "name": "Pitcher", "freebase_id": "/m/054fyh"},
    {"id": 266, "name": "Lemon", "freebase_id": "/m/09k_b"},
    {"id": 267, "name": "Invertebrate", "freebase_id": "/m/03xxp"},
    {"id": 268, "name": "Turkey", "freebase_id": "/m/0jly1"},
    {"id": 269, "name": "High heels", "freebase_id": "/m/06k2mb"},
    {"id": 270, "name": "Bust", "freebase_id": "/m/04yqq2"},
    {"id": 271, "name": "Elephant", "freebase_id": "/m/0bwd_0j"},
    {"id": 272, "name": "Scarf", "freebase_id": "/m/02h19r"},
    {"id": 273, "name": "Barrel", "freebase_id": "/m/02zn6n"},
    {"id": 274, "name": "Trombone", "freebase_id": "/m/07c6l"},
    {"id": 275, "name": "Pumpkin", "freebase_id": "/m/05zsy"},
    {"id": 276, "name": "Box", "freebase_id": "/m/025dyy"},
    {"id": 277, "name": "Tomato", "freebase_id": "/m/07j87"},
    {"id": 278, "name": "Frog", "freebase_id": "/m/09ld4"},
    {"id": 279, "name": "Bidet", "freebase_id": "/m/01vbnl"},
    {"id": 280, "name": "Human face", "freebase_id": "/m/0dzct"},
    {"id": 281, "name": "Houseplant", "freebase_id": "/m/03fp41"},
    {"id": 282, "name": "Van", "freebase_id": "/m/0h2r6"},
    {"id": 283, "name": "Shark", "freebase_id": "/m/0by6g"},
    {"id": 284, "name": "Ice cream", "freebase_id": "/m/0cxn2"},
    {"id": 285, "name": "Swim cap", "freebase_id": "/m/04tn4x"},
    {"id": 286, "name": "Falcon", "freebase_id": "/m/0f6wt"},
    {"id": 287, "name": "Ostrich", "freebase_id": "/m/05n4y"},
    {"id": 288, "name": "Handgun", "freebase_id": "/m/0gxl3"},
    {"id": 289, "name": "Whiteboard", "freebase_id": "/m/02d9qx"},
    {"id": 290, "name": "Lizard", "freebase_id": "/m/04m9y"},
    {"id": 291, "name": "Pasta", "freebase_id": "/m/05z55"},
    {"id": 292, "name": "Snowmobile", "freebase_id": "/m/01x3jk"},
    {"id": 293, "name": "Light bulb", "freebase_id": "/m/0h8l4fh"},
    {"id": 294, "name": "Window blind", "freebase_id": "/m/031b6r"},
    {"id": 295, "name": "Muffin", "freebase_id": "/m/01tcjp"},
    {"id": 296, "name": "Pretzel", "freebase_id": "/m/01f91_"},
    {"id": 297, "name": "Computer monitor", "freebase_id": "/m/02522"},
    {"id": 298, "name": "Horn", "freebase_id": "/m/0319l"},
    {"id": 299, "name": "Furniture", "freebase_id": "/m/0c_jw"},
    {"id": 300, "name": "Sandwich", "freebase_id": "/m/0l515"},
    {"id": 301, "name": "Fox", "freebase_id": "/m/0306r"},
    {"id": 302, "name": "Convenience store", "freebase_id": "/m/0crjs"},
    {"id": 303, "name": "Fish", "freebase_id": "/m/0ch_cf"},
    {"id": 304, "name": "Fruit", "freebase_id": "/m/02xwb"},
    {"id": 305, "name": "Earrings", "freebase_id": "/m/01r546"},
    {"id": 306, "name": "Curtain", "freebase_id": "/m/03rszm"},
    {"id": 307, "name": "Grape", "freebase_id": "/m/0388q"},
    {"id": 308, "name": "Sofa bed", "freebase_id": "/m/03m3pdh"},
    {"id": 309, "name": "Horse", "freebase_id": "/m/03k3r"},
    {"id": 310, "name": "Luggage and bags", "freebase_id": "/m/0hf58v5"},
    {"id": 311, "name": "Desk", "freebase_id": "/m/01y9k5"},
    {"id": 312, "name": "Crutch", "freebase_id": "/m/05441v"},
    {"id": 313, "name": "Bicycle helmet", "freebase_id": "/m/03p3bw"},
    {"id": 314, "name": "Tick", "freebase_id": "/m/0175cv"},
    {"id": 315, "name": "Airplane", "freebase_id": "/m/0cmf2"},
    {"id": 316, "name": "Canary", "freebase_id": "/m/0ccs93"},
    {"id": 317, "name": "Spatula", "freebase_id": "/m/02d1br"},
    {"id": 318, "name": "Watch", "freebase_id": "/m/0gjkl"},
    {"id": 319, "name": "Lily", "freebase_id": "/m/0jqgx"},
    {"id": 320, "name": "Kitchen appliance", "freebase_id": "/m/0h99cwc"},
    {"id": 321, "name": "Filing cabinet", "freebase_id": "/m/047j0r"},
    {"id": 322, "name": "Aircraft", "freebase_id": "/m/0k5j"},
    {"id": 323, "name": "Cake stand", "freebase_id": "/m/0h8n6ft"},
    {"id": 324, "name": "Candy", "freebase_id": "/m/0gm28"},
    {"id": 325, "name": "Sink", "freebase_id": "/m/0130jx"},
    {"id": 326, "name": "Mouse", "freebase_id": "/m/04rmv"},
    {"id": 327, "name": "Wine", "freebase_id": "/m/081qc"},
    {"id": 328, "name": "Wheelchair", "freebase_id": "/m/0qmmr"},
    {"id": 329, "name": "Goldfish", "freebase_id": "/m/03fj2"},
    {"id": 330, "name": "Refrigerator", "freebase_id": "/m/040b_t"},
    {"id": 331, "name": "French fries", "freebase_id": "/m/02y6n"},
    {"id": 332, "name": "Drawer", "freebase_id": "/m/0fqfqc"},
    {"id": 333, "name": "Treadmill", "freebase_id": "/m/030610"},
    {"id": 334, "name": "Picnic basket", "freebase_id": "/m/07kng9"},
    {"id": 335, "name": "Dice", "freebase_id": "/m/029b3"},
    {"id": 336, "name": "Cabbage", "freebase_id": "/m/0fbw6"},
    {"id": 337, "name": "Football helmet", "freebase_id": "/m/07qxg_"},
    {"id": 338, "name": "Pig", "freebase_id": "/m/068zj"},
    {"id": 339, "name": "Person", "freebase_id": "/m/01g317"},
    {"id": 340, "name": "Shorts", "freebase_id": "/m/01bfm9"},
    {"id": 341, "name": "Gondola", "freebase_id": "/m/02068x"},
    {"id": 342, "name": "Honeycomb", "freebase_id": "/m/0fz0h"},
    {"id": 343, "name": "Doughnut", "freebase_id": "/m/0jy4k"},
    {"id": 344, "name": "Chest of drawers", "freebase_id": "/m/05kyg_"},
    {"id": 345, "name": "Land vehicle", "freebase_id": "/m/01prls"},
    {"id": 346, "name": "Bat", "freebase_id": "/m/01h44"},
    {"id": 347, "name": "Monkey", "freebase_id": "/m/08pbxl"},
    {"id": 348, "name": "Dagger", "freebase_id": "/m/02gzp"},
    {"id": 349, "name": "Tableware", "freebase_id": "/m/04brg2"},
    {"id": 350, "name": "Human foot", "freebase_id": "/m/031n1"},
    {"id": 351, "name": "Mug", "freebase_id": "/m/02jvh9"},
    {"id": 352, "name": "Alarm clock", "freebase_id": "/m/046dlr"},
    {"id": 353, "name": "Pressure cooker", "freebase_id": "/m/0h8ntjv"},
    {"id": 354, "name": "Human hand", "freebase_id": "/m/0k65p"},
    {"id": 355, "name": "Tortoise", "freebase_id": "/m/011k07"},
    {"id": 356, "name": "Baseball glove", "freebase_id": "/m/03grzl"},
    {"id": 357, "name": "Sword", "freebase_id": "/m/06y5r"},
    {"id": 358, "name": "Pear", "freebase_id": "/m/061_f"},
    {"id": 359, "name": "Miniskirt", "freebase_id": "/m/01cmb2"},
    {"id": 360, "name": "Traffic sign", "freebase_id": "/m/01mqdt"},
    {"id": 361, "name": "Girl", "freebase_id": "/m/05r655"},
    {"id": 362, "name": "Roller skates", "freebase_id": "/m/02p3w7d"},
    {"id": 363, "name": "Dinosaur", "freebase_id": "/m/029tx"},
    {"id": 364, "name": "Porch", "freebase_id": "/m/04m6gz"},
    {"id": 365, "name": "Human beard", "freebase_id": "/m/015h_t"},
    {"id": 366, "name": "Submarine sandwich", "freebase_id": "/m/06pcq"},
    {"id": 367, "name": "Screwdriver", "freebase_id": "/m/01bms0"},
    {"id": 368, "name": "Strawberry", "freebase_id": "/m/07fbm7"},
    {"id": 369, "name": "Wine glass", "freebase_id": "/m/09tvcd"},
    {"id": 370, "name": "Seafood", "freebase_id": "/m/06nwz"},
    {"id": 371, "name": "Racket", "freebase_id": "/m/0dv9c"},
    {"id": 372, "name": "Wheel", "freebase_id": "/m/083wq"},
    {"id": 373, "name": "Sea lion", "freebase_id": "/m/0gd36"},
    {"id": 374, "name": "Toy", "freebase_id": "/m/0138tl"},
    {"id": 375, "name": "Tea", "freebase_id": "/m/07clx"},
    {"id": 376, "name": "Tennis ball", "freebase_id": "/m/05ctyq"},
    {"id": 377, "name": "Waste container", "freebase_id": "/m/0bjyj5"},
    {"id": 378, "name": "Mule", "freebase_id": "/m/0dbzx"},
    {"id": 379, "name": "Cricket ball", "freebase_id": "/m/02ctlc"},
    {"id": 380, "name": "Pineapple", "freebase_id": "/m/0fp6w"},
    {"id": 381, "name": "Coconut", "freebase_id": "/m/0djtd"},
    {"id": 382, "name": "Doll", "freebase_id": "/m/0167gd"},
    {"id": 383, "name": "Coffee table", "freebase_id": "/m/078n6m"},
    {"id": 384, "name": "Snowman", "freebase_id": "/m/0152hh"},
    {"id": 385, "name": "Lavender", "freebase_id": "/m/04gth"},
    {"id": 386, "name": "Shrimp", "freebase_id": "/m/0ll1f78"},
    {"id": 387, "name": "Maple", "freebase_id": "/m/0cffdh"},
    {"id": 388, "name": "Cowboy hat", "freebase_id": "/m/025rp__"},
    {"id": 389, "name": "Goggles", "freebase_id": "/m/02_n6y"},
    {"id": 390, "name": "Rugby ball", "freebase_id": "/m/0wdt60w"},
    {"id": 391, "name": "Caterpillar", "freebase_id": "/m/0cydv"},
    {"id": 392, "name": "Poster", "freebase_id": "/m/01n5jq"},
    {"id": 393, "name": "Rocket", "freebase_id": "/m/09rvcxw"},
    {"id": 394, "name": "Organ", "freebase_id": "/m/013y1f"},
    {"id": 395, "name": "Saxophone", "freebase_id": "/m/06ncr"},
    {"id": 396, "name": "Traffic light", "freebase_id": "/m/015qff"},
    {"id": 397, "name": "Cocktail", "freebase_id": "/m/024g6"},
    {"id": 398, "name": "Plastic bag", "freebase_id": "/m/05gqfk"},
    {"id": 399, "name": "Squash", "freebase_id": "/m/0dv77"},
    {"id": 400, "name": "Mushroom", "freebase_id": "/m/052sf"},
    {"id": 401, "name": "Hamburger", "freebase_id": "/m/0cdn1"},
    {"id": 402, "name": "Light switch", "freebase_id": "/m/03jbxj"},
    {"id": 403, "name": "Parachute", "freebase_id": "/m/0cyfs"},
    {"id": 404, "name": "Teddy bear", "freebase_id": "/m/0kmg4"},
    {"id": 405, "name": "Winter melon", "freebase_id": "/m/02cvgx"},
    {"id": 406, "name": "Deer", "freebase_id": "/m/09kx5"},
    {"id": 407, "name": "Musical keyboard", "freebase_id": "/m/057cc"},
    {"id": 408, "name": "Plumbing fixture", "freebase_id": "/m/02pkr5"},
    {"id": 409, "name": "Scoreboard", "freebase_id": "/m/057p5t"},
    {"id": 410, "name": "Baseball bat", "freebase_id": "/m/03g8mr"},
    {"id": 411, "name": "Envelope", "freebase_id": "/m/0frqm"},
    {"id": 412, "name": "Adhesive tape", "freebase_id": "/m/03m3vtv"},
    {"id": 413, "name": "Briefcase", "freebase_id": "/m/0584n8"},
    {"id": 414, "name": "Paddle", "freebase_id": "/m/014y4n"},
    {"id": 415, "name": "Bow and arrow", "freebase_id": "/m/01g3x7"},
    {"id": 416, "name": "Telephone", "freebase_id": "/m/07cx4"},
    {"id": 417, "name": "Sheep", "freebase_id": "/m/07bgp"},
    {"id": 418, "name": "Jacket", "freebase_id": "/m/032b3c"},
    {"id": 419, "name": "Boy", "freebase_id": "/m/01bl7v"},
    {"id": 420, "name": "Pizza", "freebase_id": "/m/0663v"},
    {"id": 421, "name": "Otter", "freebase_id": "/m/0cn6p"},
    {"id": 422, "name": "Office supplies", "freebase_id": "/m/02rdsp"},
    {"id": 423, "name": "Couch", "freebase_id": "/m/02crq1"},
    {"id": 424, "name": "Cello", "freebase_id": "/m/01xqw"},
    {"id": 425, "name": "Bull", "freebase_id": "/m/0cnyhnx"},
    {"id": 426, "name": "Camel", "freebase_id": "/m/01x_v"},
    {"id": 427, "name": "Ball", "freebase_id": "/m/018xm"},
    {"id": 428, "name": "Duck", "freebase_id": "/m/09ddx"},
    {"id": 429, "name": "Whale", "freebase_id": "/m/084zz"},
    {"id": 430, "name": "Shirt", "freebase_id": "/m/01n4qj"},
    {"id": 431, "name": "Tank", "freebase_id": "/m/07cmd"},
    {"id": 432, "name": "Motorcycle", "freebase_id": "/m/04_sv"},
    {"id": 433, "name": "Accordion", "freebase_id": "/m/0mkg"},
    {"id": 434, "name": "Owl", "freebase_id": "/m/09d5_"},
    {"id": 435, "name": "Porcupine", "freebase_id": "/m/0c568"},
    {"id": 436, "name": "Sun hat", "freebase_id": "/m/02wbtzl"},
    {"id": 437, "name": "Nail", "freebase_id": "/m/05bm6"},
    {"id": 438, "name": "Scissors", "freebase_id": "/m/01lsmm"},
    {"id": 439, "name": "Swan", "freebase_id": "/m/0dftk"},
    {"id": 440, "name": "Lamp", "freebase_id": "/m/0dtln"},
    {"id": 441, "name": "Crown", "freebase_id": "/m/0nl46"},
    {"id": 442, "name": "Piano", "freebase_id": "/m/05r5c"},
    {"id": 443, "name": "Sculpture", "freebase_id": "/m/06msq"},
    {"id": 444, "name": "Cheetah", "freebase_id": "/m/0cd4d"},
    {"id": 445, "name": "Oboe", "freebase_id": "/m/05kms"},
    {"id": 446, "name": "Tin can", "freebase_id": "/m/02jnhm"},
    {"id": 447, "name": "Mango", "freebase_id": "/m/0fldg"},
    {"id": 448, "name": "Tripod", "freebase_id": "/m/073bxn"},
    {"id": 449, "name": "Oven", "freebase_id": "/m/029bxz"},
    {"id": 450, "name": "Mouse", "freebase_id": "/m/020lf"},
    {"id": 451, "name": "Barge", "freebase_id": "/m/01btn"},
    {"id": 452, "name": "Coffee", "freebase_id": "/m/02vqfm"},
    {"id": 453, "name": "Snowboard", "freebase_id": "/m/06__v"},
    {"id": 454, "name": "Common fig", "freebase_id": "/m/043nyj"},
    {"id": 455, "name": "Salad", "freebase_id": "/m/0grw1"},
    {"id": 456, "name": "Marine invertebrates", "freebase_id": "/m/03hl4l9"},
    {"id": 457, "name": "Umbrella", "freebase_id": "/m/0hnnb"},
    {"id": 458, "name": "Kangaroo", "freebase_id": "/m/04c0y"},
    {"id": 459, "name": "Human arm", "freebase_id": "/m/0dzf4"},
    {"id": 460, "name": "Measuring cup", "freebase_id": "/m/07v9_z"},
    {"id": 461, "name": "Snail", "freebase_id": "/m/0f9_l"},
    {"id": 462, "name": "Loveseat", "freebase_id": "/m/0703r8"},
    {"id": 463, "name": "Suit", "freebase_id": "/m/01xyhv"},
    {"id": 464, "name": "Teapot", "freebase_id": "/m/01fh4r"},
    {"id": 465, "name": "Bottle", "freebase_id": "/m/04dr76w"},
    {"id": 466, "name": "Alpaca", "freebase_id": "/m/0pcr"},
    {"id": 467, "name": "Kettle", "freebase_id": "/m/03s_tn"},
    {"id": 468, "name": "Trousers", "freebase_id": "/m/07mhn"},
    {"id": 469, "name": "Popcorn", "freebase_id": "/m/01hrv5"},
    {"id": 470, "name": "Centipede", "freebase_id": "/m/019h78"},
    {"id": 471, "name": "Spider", "freebase_id": "/m/09kmb"},
    {"id": 472, "name": "Sparrow", "freebase_id": "/m/0h23m"},
    {"id": 473, "name": "Plate", "freebase_id": "/m/050gv4"},
    {"id": 474, "name": "Bagel", "freebase_id": "/m/01fb_0"},
    {"id": 475, "name": "Personal care", "freebase_id": "/m/02w3_ws"},
    {"id": 476, "name": "Apple", "freebase_id": "/m/014j1m"},
    {"id": 477, "name": "Brassiere", "freebase_id": "/m/01gmv2"},
    {"id": 478, "name": "Bathroom cabinet", "freebase_id": "/m/04y4h8h"},
    {"id": 479, "name": "studio couch", "freebase_id": "/m/026qbn5"},
    {"id": 480, "name": "Computer keyboard", "freebase_id": "/m/01m2v"},
    {"id": 481, "name": "Table tennis racket", "freebase_id": "/m/05_5p_0"},
    {"id": 482, "name": "Sushi", "freebase_id": "/m/07030"},
    {"id": 483, "name": "Cabinetry", "freebase_id": "/m/01s105"},
    {"id": 484, "name": "Street light", "freebase_id": "/m/033rq4"},
    {"id": 485, "name": "Towel", "freebase_id": "/m/0162_1"},
    {"id": 486, "name": "Nightstand", "freebase_id": "/m/02z51p"},
    {"id": 487, "name": "Rabbit", "freebase_id": "/m/06mf6"},
    {"id": 488, "name": "Dolphin", "freebase_id": "/m/02hj4"},
    {"id": 489, "name": "Dog", "freebase_id": "/m/0bt9lr"},
    {"id": 490, "name": "Jug", "freebase_id": "/m/08hvt4"},
    {"id": 491, "name": "Wok", "freebase_id": "/m/084rd"},
    {"id": 492, "name": "Fire hydrant", "freebase_id": "/m/01pns0"},
    {"id": 493, "name": "Human eye", "freebase_id": "/m/014sv8"},
    {"id": 494, "name": "Skyscraper", "freebase_id": "/m/079cl"},
    {"id": 495, "name": "Backpack", "freebase_id": "/m/01940j"},
    {"id": 496, "name": "Potato", "freebase_id": "/m/05vtc"},
    {"id": 497, "name": "Paper towel", "freebase_id": "/m/02w3r3"},
    {"id": 498, "name": "Lifejacket", "freebase_id": "/m/054xkw"},
    {"id": 499, "name": "Bicycle wheel", "freebase_id": "/m/01bqk0"},
    {"id": 500, "name": "Toilet", "freebase_id": "/m/09g1w"},
]


OPENIMAGES_V6_CATEGORIES = [
    {"id": 1, "name": "Tortoise", "freebase_id": "/m/011k07"},
    {"id": 2, "name": "Container", "freebase_id": "/m/011q46kg"},
    {"id": 3, "name": "Magpie", "freebase_id": "/m/012074"},
    {"id": 4, "name": "Sea turtle", "freebase_id": "/m/0120dh"},
    {"id": 5, "name": "Football", "freebase_id": "/m/01226z"},
    {"id": 6, "name": "Ambulance", "freebase_id": "/m/012n7d"},
    {"id": 7, "name": "Ladder", "freebase_id": "/m/012w5l"},
    {"id": 8, "name": "Toothbrush", "freebase_id": "/m/012xff"},
    {"id": 9, "name": "Syringe", "freebase_id": "/m/012ysf"},
    {"id": 10, "name": "Sink", "freebase_id": "/m/0130jx"},
    {"id": 11, "name": "Toy", "freebase_id": "/m/0138tl"},
    {"id": 12, "name": "Organ (Musical Instrument)", "freebase_id": "/m/013y1f"},
    {"id": 13, "name": "Cassette deck", "freebase_id": "/m/01432t"},
    {"id": 14, "name": "Apple", "freebase_id": "/m/014j1m"},
    {"id": 15, "name": "Human eye", "freebase_id": "/m/014sv8"},
    {"id": 16, "name": "Cosmetics", "freebase_id": "/m/014trl"},
    {"id": 17, "name": "Paddle", "freebase_id": "/m/014y4n"},
    {"id": 18, "name": "Snowman", "freebase_id": "/m/0152hh"},
    {"id": 19, "name": "Beer", "freebase_id": "/m/01599"},
    {"id": 20, "name": "Chopsticks", "freebase_id": "/m/01_5g"},
    {"id": 21, "name": "Human beard", "freebase_id": "/m/015h_t"},
    {"id": 22, "name": "Bird", "freebase_id": "/m/015p6"},
    {"id": 23, "name": "Parking meter", "freebase_id": "/m/015qbp"},
    {"id": 24, "name": "Traffic light", "freebase_id": "/m/015qff"},
    {"id": 25, "name": "Croissant", "freebase_id": "/m/015wgc"},
    {"id": 26, "name": "Cucumber", "freebase_id": "/m/015x4r"},
    {"id": 27, "name": "Radish", "freebase_id": "/m/015x5n"},
    {"id": 28, "name": "Towel", "freebase_id": "/m/0162_1"},
    {"id": 29, "name": "Doll", "freebase_id": "/m/0167gd"},
    {"id": 30, "name": "Skull", "freebase_id": "/m/016m2d"},
    {"id": 31, "name": "Washing machine", "freebase_id": "/m/0174k2"},
    {"id": 32, "name": "Glove", "freebase_id": "/m/0174n1"},
    {"id": 33, "name": "Tick", "freebase_id": "/m/0175cv"},
    {"id": 34, "name": "Belt", "freebase_id": "/m/0176mf"},
    {"id": 35, "name": "Sunglasses", "freebase_id": "/m/017ftj"},
    {"id": 36, "name": "Banjo", "freebase_id": "/m/018j2"},
    {"id": 37, "name": "Cart", "freebase_id": "/m/018p4k"},
    {"id": 38, "name": "Ball", "freebase_id": "/m/018xm"},
    {"id": 39, "name": "Backpack", "freebase_id": "/m/01940j"},
    {"id": 40, "name": "Bicycle", "freebase_id": "/m/0199g"},
    {"id": 41, "name": "Home appliance", "freebase_id": "/m/019dx1"},
    {"id": 42, "name": "Centipede", "freebase_id": "/m/019h78"},
    {"id": 43, "name": "Boat", "freebase_id": "/m/019jd"},
    {"id": 44, "name": "Surfboard", "freebase_id": "/m/019w40"},
    {"id": 45, "name": "Boot", "freebase_id": "/m/01b638"},
    {"id": 46, "name": "Headphones", "freebase_id": "/m/01b7fy"},
    {"id": 47, "name": "Hot dog", "freebase_id": "/m/01b9xk"},
    {"id": 48, "name": "Shorts", "freebase_id": "/m/01bfm9"},
    {"id": 49, "name": "Fast food", "freebase_id": "/m/01_bhs"},
    {"id": 50, "name": "Bus", "freebase_id": "/m/01bjv"},
    {"id": 51, "name": "Boy", "freebase_id": "/m/01bl7v"},
    {"id": 52, "name": "Screwdriver", "freebase_id": "/m/01bms0"},
    {"id": 53, "name": "Bicycle wheel", "freebase_id": "/m/01bqk0"},
    {"id": 54, "name": "Barge", "freebase_id": "/m/01btn"},
    {"id": 55, "name": "Laptop", "freebase_id": "/m/01c648"},
    {"id": 56, "name": "Miniskirt", "freebase_id": "/m/01cmb2"},
    {"id": 57, "name": "Drill (Tool)", "freebase_id": "/m/01d380"},
    {"id": 58, "name": "Dress", "freebase_id": "/m/01d40f"},
    {"id": 59, "name": "Bear", "freebase_id": "/m/01dws"},
    {"id": 60, "name": "Waffle", "freebase_id": "/m/01dwsz"},
    {"id": 61, "name": "Pancake", "freebase_id": "/m/01dwwc"},
    {"id": 62, "name": "Brown bear", "freebase_id": "/m/01dxs"},
    {"id": 63, "name": "Woodpecker", "freebase_id": "/m/01dy8n"},
    {"id": 64, "name": "Blue jay", "freebase_id": "/m/01f8m5"},
    {"id": 65, "name": "Pretzel", "freebase_id": "/m/01f91_"},
    {"id": 66, "name": "Bagel", "freebase_id": "/m/01fb_0"},
    {"id": 67, "name": "Tower", "freebase_id": "/m/01fdzj"},
    {"id": 68, "name": "Teapot", "freebase_id": "/m/01fh4r"},
    {"id": 69, "name": "Person", "freebase_id": "/m/01g317"},
    {"id": 70, "name": "Bow and arrow", "freebase_id": "/m/01g3x7"},
    {"id": 71, "name": "Swimwear", "freebase_id": "/m/01gkx_"},
    {"id": 72, "name": "Beehive", "freebase_id": "/m/01gllr"},
    {"id": 73, "name": "Brassiere", "freebase_id": "/m/01gmv2"},
    {"id": 74, "name": "Bee", "freebase_id": "/m/01h3n"},
    {"id": 75, "name": "Bat (Animal)", "freebase_id": "/m/01h44"},
    {"id": 76, "name": "Starfish", "freebase_id": "/m/01h8tj"},
    {"id": 77, "name": "Popcorn", "freebase_id": "/m/01hrv5"},
    {"id": 78, "name": "Burrito", "freebase_id": "/m/01j3zr"},
    {"id": 79, "name": "Chainsaw", "freebase_id": "/m/01j4z9"},
    {"id": 80, "name": "Balloon", "freebase_id": "/m/01j51"},
    {"id": 81, "name": "Wrench", "freebase_id": "/m/01j5ks"},
    {"id": 82, "name": "Tent", "freebase_id": "/m/01j61q"},
    {"id": 83, "name": "Vehicle registration plate", "freebase_id": "/m/01jfm_"},
    {"id": 84, "name": "Lantern", "freebase_id": "/m/01jfsr"},
    {"id": 85, "name": "Toaster", "freebase_id": "/m/01k6s3"},
    {"id": 86, "name": "Flashlight", "freebase_id": "/m/01kb5b"},
    {"id": 87, "name": "Billboard", "freebase_id": "/m/01knjb"},
    {"id": 88, "name": "Tiara", "freebase_id": "/m/01krhy"},
    {"id": 89, "name": "Limousine", "freebase_id": "/m/01lcw4"},
    {"id": 90, "name": "Necklace", "freebase_id": "/m/01llwg"},
    {"id": 91, "name": "Carnivore", "freebase_id": "/m/01lrl"},
    {"id": 92, "name": "Scissors", "freebase_id": "/m/01lsmm"},
    {"id": 93, "name": "Stairs", "freebase_id": "/m/01lynh"},
    {"id": 94, "name": "Computer keyboard", "freebase_id": "/m/01m2v"},
    {"id": 95, "name": "Printer", "freebase_id": "/m/01m4t"},
    {"id": 96, "name": "Traffic sign", "freebase_id": "/m/01mqdt"},
    {"id": 97, "name": "Chair", "freebase_id": "/m/01mzpv"},
    {"id": 98, "name": "Shirt", "freebase_id": "/m/01n4qj"},
    {"id": 99, "name": "Poster", "freebase_id": "/m/01n5jq"},
    {"id": 100, "name": "Cheese", "freebase_id": "/m/01nkt"},
    {"id": 101, "name": "Sock", "freebase_id": "/m/01nq26"},
    {"id": 102, "name": "Fire hydrant", "freebase_id": "/m/01pns0"},
    {"id": 103, "name": "Land vehicle", "freebase_id": "/m/01prls"},
    {"id": 104, "name": "Earrings", "freebase_id": "/m/01r546"},
    {"id": 105, "name": "Tie", "freebase_id": "/m/01rkbr"},
    {"id": 106, "name": "Watercraft", "freebase_id": "/m/01rzcn"},
    {"id": 107, "name": "Cabinetry", "freebase_id": "/m/01s105"},
    {"id": 108, "name": "Suitcase", "freebase_id": "/m/01s55n"},
    {"id": 109, "name": "Muffin", "freebase_id": "/m/01tcjp"},
    {"id": 110, "name": "Bidet", "freebase_id": "/m/01vbnl"},
    {"id": 111, "name": "Snack", "freebase_id": "/m/01ww8y"},
    {"id": 112, "name": "Snowmobile", "freebase_id": "/m/01x3jk"},
    {"id": 113, "name": "Clock", "freebase_id": "/m/01x3z"},
    {"id": 114, "name": "Medical equipment", "freebase_id": "/m/01xgg_"},
    {"id": 115, "name": "Cattle", "freebase_id": "/m/01xq0k1"},
    {"id": 116, "name": "Cello", "freebase_id": "/m/01xqw"},
    {"id": 117, "name": "Jet ski", "freebase_id": "/m/01xs3r"},
    {"id": 118, "name": "Camel", "freebase_id": "/m/01x_v"},
    {"id": 119, "name": "Coat", "freebase_id": "/m/01xygc"},
    {"id": 120, "name": "Suit", "freebase_id": "/m/01xyhv"},
    {"id": 121, "name": "Desk", "freebase_id": "/m/01y9k5"},
    {"id": 122, "name": "Cat", "freebase_id": "/m/01yrx"},
    {"id": 123, "name": "Bronze sculpture", "freebase_id": "/m/01yx86"},
    {"id": 124, "name": "Juice", "freebase_id": "/m/01z1kdw"},
    {"id": 125, "name": "Gondola", "freebase_id": "/m/02068x"},
    {"id": 126, "name": "Beetle", "freebase_id": "/m/020jm"},
    {"id": 127, "name": "Cannon", "freebase_id": "/m/020kz"},
    {"id": 128, "name": "Computer mouse", "freebase_id": "/m/020lf"},
    {"id": 129, "name": "Cookie", "freebase_id": "/m/021mn"},
    {"id": 130, "name": "Office building", "freebase_id": "/m/021sj1"},
    {"id": 131, "name": "Fountain", "freebase_id": "/m/0220r2"},
    {"id": 132, "name": "Coin", "freebase_id": "/m/0242l"},
    {"id": 133, "name": "Calculator", "freebase_id": "/m/024d2"},
    {"id": 134, "name": "Cocktail", "freebase_id": "/m/024g6"},
    {"id": 135, "name": "Computer monitor", "freebase_id": "/m/02522"},
    {"id": 136, "name": "Box", "freebase_id": "/m/025dyy"},
    {"id": 137, "name": "Stapler", "freebase_id": "/m/025fsf"},
    {"id": 138, "name": "Christmas tree", "freebase_id": "/m/025nd"},
    {"id": 139, "name": "Cowboy hat", "freebase_id": "/m/025rp__"},
    {"id": 140, "name": "Hiking equipment", "freebase_id": "/m/0268lbt"},
    {"id": 141, "name": "Studio couch", "freebase_id": "/m/026qbn5"},
    {"id": 142, "name": "Drum", "freebase_id": "/m/026t6"},
    {"id": 143, "name": "Dessert", "freebase_id": "/m/0270h"},
    {"id": 144, "name": "Wine rack", "freebase_id": "/m/0271qf7"},
    {"id": 145, "name": "Drink", "freebase_id": "/m/0271t"},
    {"id": 146, "name": "Zucchini", "freebase_id": "/m/027pcv"},
    {"id": 147, "name": "Ladle", "freebase_id": "/m/027rl48"},
    {"id": 148, "name": "Human mouth", "freebase_id": "/m/0283dt1"},
    {"id": 149, "name": "Dairy Product", "freebase_id": "/m/0284d"},
    {"id": 150, "name": "Dice", "freebase_id": "/m/029b3"},
    {"id": 151, "name": "Oven", "freebase_id": "/m/029bxz"},
    {"id": 152, "name": "Dinosaur", "freebase_id": "/m/029tx"},
    {"id": 153, "name": "Ratchet (Device)", "freebase_id": "/m/02bm9n"},
    {"id": 154, "name": "Couch", "freebase_id": "/m/02crq1"},
    {"id": 155, "name": "Cricket ball", "freebase_id": "/m/02ctlc"},
    {"id": 156, "name": "Winter melon", "freebase_id": "/m/02cvgx"},
    {"id": 157, "name": "Spatula", "freebase_id": "/m/02d1br"},
    {"id": 158, "name": "Whiteboard", "freebase_id": "/m/02d9qx"},
    {"id": 159, "name": "Pencil sharpener", "freebase_id": "/m/02ddwp"},
    {"id": 160, "name": "Door", "freebase_id": "/m/02dgv"},
    {"id": 161, "name": "Hat", "freebase_id": "/m/02dl1y"},
    {"id": 162, "name": "Shower", "freebase_id": "/m/02f9f_"},
    {"id": 163, "name": "Eraser", "freebase_id": "/m/02fh7f"},
    {"id": 164, "name": "Fedora", "freebase_id": "/m/02fq_6"},
    {"id": 165, "name": "Guacamole", "freebase_id": "/m/02g30s"},
    {"id": 166, "name": "Dagger", "freebase_id": "/m/02gzp"},
    {"id": 167, "name": "Scarf", "freebase_id": "/m/02h19r"},
    {"id": 168, "name": "Dolphin", "freebase_id": "/m/02hj4"},
    {"id": 169, "name": "Sombrero", "freebase_id": "/m/02jfl0"},
    {"id": 170, "name": "Tin can", "freebase_id": "/m/02jnhm"},
    {"id": 171, "name": "Mug", "freebase_id": "/m/02jvh9"},
    {"id": 172, "name": "Tap", "freebase_id": "/m/02jz0l"},
    {"id": 173, "name": "Harbor seal", "freebase_id": "/m/02l8p9"},
    {"id": 174, "name": "Stretcher", "freebase_id": "/m/02lbcq"},
    {"id": 175, "name": "Can opener", "freebase_id": "/m/02mqfb"},
    {"id": 176, "name": "Goggles", "freebase_id": "/m/02_n6y"},
    {"id": 177, "name": "Human body", "freebase_id": "/m/02p0tk3"},
    {"id": 178, "name": "Roller skates", "freebase_id": "/m/02p3w7d"},
    {"id": 179, "name": "Coffee cup", "freebase_id": "/m/02p5f1q"},
    {"id": 180, "name": "Cutting board", "freebase_id": "/m/02pdsw"},
    {"id": 181, "name": "Blender", "freebase_id": "/m/02pjr4"},
    {"id": 182, "name": "Plumbing fixture", "freebase_id": "/m/02pkr5"},
    {"id": 183, "name": "Stop sign", "freebase_id": "/m/02pv19"},
    {"id": 184, "name": "Office supplies", "freebase_id": "/m/02rdsp"},
    {"id": 185, "name": "Volleyball (Ball)", "freebase_id": "/m/02rgn06"},
    {"id": 186, "name": "Vase", "freebase_id": "/m/02s195"},
    {"id": 187, "name": "Slow cooker", "freebase_id": "/m/02tsc9"},
    {"id": 188, "name": "Wardrobe", "freebase_id": "/m/02vkqh8"},
    {"id": 189, "name": "Coffee", "freebase_id": "/m/02vqfm"},
    {"id": 190, "name": "Whisk", "freebase_id": "/m/02vwcm"},
    {"id": 191, "name": "Paper towel", "freebase_id": "/m/02w3r3"},
    {"id": 192, "name": "Personal care", "freebase_id": "/m/02w3_ws"},
    {"id": 193, "name": "Food", "freebase_id": "/m/02wbm"},
    {"id": 194, "name": "Sun hat", "freebase_id": "/m/02wbtzl"},
    {"id": 195, "name": "Tree house", "freebase_id": "/m/02wg_p"},
    {"id": 196, "name": "Flying disc", "freebase_id": "/m/02wmf"},
    {"id": 197, "name": "Skirt", "freebase_id": "/m/02wv6h6"},
    {"id": 198, "name": "Gas stove", "freebase_id": "/m/02wv84t"},
    {"id": 199, "name": "Salt and pepper shakers", "freebase_id": "/m/02x8cch"},
    {"id": 200, "name": "Mechanical fan", "freebase_id": "/m/02x984l"},
    {"id": 201, "name": "Face powder", "freebase_id": "/m/02xb7qb"},
    {"id": 202, "name": "Fax", "freebase_id": "/m/02xqq"},
    {"id": 203, "name": "Fruit", "freebase_id": "/m/02xwb"},
    {"id": 204, "name": "French fries", "freebase_id": "/m/02y6n"},
    {"id": 205, "name": "Nightstand", "freebase_id": "/m/02z51p"},
    {"id": 206, "name": "Barrel", "freebase_id": "/m/02zn6n"},
    {"id": 207, "name": "Kite", "freebase_id": "/m/02zt3"},
    {"id": 208, "name": "Tart", "freebase_id": "/m/02zvsm"},
    {"id": 209, "name": "Treadmill", "freebase_id": "/m/030610"},
    {"id": 210, "name": "Fox", "freebase_id": "/m/0306r"},
    {"id": 211, "name": "Flag", "freebase_id": "/m/03120"},
    {"id": 212, "name": "French horn", "freebase_id": "/m/0319l"},
    {"id": 213, "name": "Window blind", "freebase_id": "/m/031b6r"},
    {"id": 214, "name": "Human foot", "freebase_id": "/m/031n1"},
    {"id": 215, "name": "Golf cart", "freebase_id": "/m/0323sq"},
    {"id": 216, "name": "Jacket", "freebase_id": "/m/032b3c"},
    {"id": 217, "name": "Egg (Food)", "freebase_id": "/m/033cnk"},
    {"id": 218, "name": "Street light", "freebase_id": "/m/033rq4"},
    {"id": 219, "name": "Guitar", "freebase_id": "/m/0342h"},
    {"id": 220, "name": "Pillow", "freebase_id": "/m/034c16"},
    {"id": 221, "name": "Human leg", "freebase_id": "/m/035r7c"},
    {"id": 222, "name": "Isopod", "freebase_id": "/m/035vxb"},
    {"id": 223, "name": "Grape", "freebase_id": "/m/0388q"},
    {"id": 224, "name": "Human ear", "freebase_id": "/m/039xj_"},
    {"id": 225, "name": "Power plugs and sockets", "freebase_id": "/m/03bbps"},
    {"id": 226, "name": "Panda", "freebase_id": "/m/03bj1"},
    {"id": 227, "name": "Giraffe", "freebase_id": "/m/03bk1"},
    {"id": 228, "name": "Woman", "freebase_id": "/m/03bt1vf"},
    {"id": 229, "name": "Door handle", "freebase_id": "/m/03c7gz"},
    {"id": 230, "name": "Rhinoceros", "freebase_id": "/m/03d443"},
    {"id": 231, "name": "Bathtub", "freebase_id": "/m/03dnzn"},
    {"id": 232, "name": "Goldfish", "freebase_id": "/m/03fj2"},
    {"id": 233, "name": "Houseplant", "freebase_id": "/m/03fp41"},
    {"id": 234, "name": "Goat", "freebase_id": "/m/03fwl"},
    {"id": 235, "name": "Baseball bat", "freebase_id": "/m/03g8mr"},
    {"id": 236, "name": "Baseball glove", "freebase_id": "/m/03grzl"},
    {"id": 237, "name": "Mixing bowl", "freebase_id": "/m/03hj559"},
    {"id": 238, "name": "Marine invertebrates", "freebase_id": "/m/03hl4l9"},
    {"id": 239, "name": "Kitchen utensil", "freebase_id": "/m/03hlz0c"},
    {"id": 240, "name": "Light switch", "freebase_id": "/m/03jbxj"},
    {"id": 241, "name": "House", "freebase_id": "/m/03jm5"},
    {"id": 242, "name": "Horse", "freebase_id": "/m/03k3r"},
    {"id": 243, "name": "Stationary bicycle", "freebase_id": "/m/03kt2w"},
    {"id": 244, "name": "Hammer", "freebase_id": "/m/03l9g"},
    {"id": 245, "name": "Ceiling fan", "freebase_id": "/m/03ldnb"},
    {"id": 246, "name": "Sofa bed", "freebase_id": "/m/03m3pdh"},
    {"id": 247, "name": "Adhesive tape", "freebase_id": "/m/03m3vtv"},
    {"id": 248, "name": "Harp", "freebase_id": "/m/03m5k"},
    {"id": 249, "name": "Sandal", "freebase_id": "/m/03nfch"},
    {"id": 250, "name": "Bicycle helmet", "freebase_id": "/m/03p3bw"},
    {"id": 251, "name": "Saucer", "freebase_id": "/m/03q5c7"},
    {"id": 252, "name": "Harpsichord", "freebase_id": "/m/03q5t"},
    {"id": 253, "name": "Human hair", "freebase_id": "/m/03q69"},
    {"id": 254, "name": "Heater", "freebase_id": "/m/03qhv5"},
    {"id": 255, "name": "Harmonica", "freebase_id": "/m/03qjg"},
    {"id": 256, "name": "Hamster", "freebase_id": "/m/03qrc"},
    {"id": 257, "name": "Curtain", "freebase_id": "/m/03rszm"},
    {"id": 258, "name": "Bed", "freebase_id": "/m/03ssj5"},
    {"id": 259, "name": "Kettle", "freebase_id": "/m/03s_tn"},
    {"id": 260, "name": "Fireplace", "freebase_id": "/m/03tw93"},
    {"id": 261, "name": "Scale", "freebase_id": "/m/03txqz"},
    {"id": 262, "name": "Drinking straw", "freebase_id": "/m/03v5tg"},
    {"id": 263, "name": "Insect", "freebase_id": "/m/03vt0"},
    {"id": 264, "name": "Hair dryer", "freebase_id": "/m/03wvsk"},
    {"id": 265, "name": "Kitchenware", "freebase_id": "/m/03_wxk"},
    {"id": 266, "name": "Indoor rower", "freebase_id": "/m/03wym"},
    {"id": 267, "name": "Invertebrate", "freebase_id": "/m/03xxp"},
    {"id": 268, "name": "Food processor", "freebase_id": "/m/03y6mg"},
    {"id": 269, "name": "Bookcase", "freebase_id": "/m/03__z0"},
    {"id": 270, "name": "Refrigerator", "freebase_id": "/m/040b_t"},
    {"id": 271, "name": "Wood-burning stove", "freebase_id": "/m/04169hn"},
    {"id": 272, "name": "Punching bag", "freebase_id": "/m/0420v5"},
    {"id": 273, "name": "Common fig", "freebase_id": "/m/043nyj"},
    {"id": 274, "name": "Cocktail shaker", "freebase_id": "/m/0440zs"},
    {"id": 275, "name": "Jaguar (Animal)", "freebase_id": "/m/0449p"},
    {"id": 276, "name": "Golf ball", "freebase_id": "/m/044r5d"},
    {"id": 277, "name": "Fashion accessory", "freebase_id": "/m/0463sg"},
    {"id": 278, "name": "Alarm clock", "freebase_id": "/m/046dlr"},
    {"id": 279, "name": "Filing cabinet", "freebase_id": "/m/047j0r"},
    {"id": 280, "name": "Artichoke", "freebase_id": "/m/047v4b"},
    {"id": 281, "name": "Table", "freebase_id": "/m/04bcr3"},
    {"id": 282, "name": "Tableware", "freebase_id": "/m/04brg2"},
    {"id": 283, "name": "Kangaroo", "freebase_id": "/m/04c0y"},
    {"id": 284, "name": "Koala", "freebase_id": "/m/04cp_"},
    {"id": 285, "name": "Knife", "freebase_id": "/m/04ctx"},
    {"id": 286, "name": "Bottle", "freebase_id": "/m/04dr76w"},
    {"id": 287, "name": "Bottle opener", "freebase_id": "/m/04f5ws"},
    {"id": 288, "name": "Lynx", "freebase_id": "/m/04g2r"},
    {"id": 289, "name": "Lavender (Plant)", "freebase_id": "/m/04gth"},
    {"id": 290, "name": "Lighthouse", "freebase_id": "/m/04h7h"},
    {"id": 291, "name": "Dumbbell", "freebase_id": "/m/04h8sr"},
    {"id": 292, "name": "Human head", "freebase_id": "/m/04hgtk"},
    {"id": 293, "name": "Bowl", "freebase_id": "/m/04kkgm"},
    {"id": 294, "name": "Humidifier", "freebase_id": "/m/04lvq_"},
    {"id": 295, "name": "Porch", "freebase_id": "/m/04m6gz"},
    {"id": 296, "name": "Lizard", "freebase_id": "/m/04m9y"},
    {"id": 297, "name": "Billiard table", "freebase_id": "/m/04p0qw"},
    {"id": 298, "name": "Mammal", "freebase_id": "/m/04rky"},
    {"id": 299, "name": "Mouse", "freebase_id": "/m/04rmv"},
    {"id": 300, "name": "Motorcycle", "freebase_id": "/m/04_sv"},
    {"id": 301, "name": "Musical instrument", "freebase_id": "/m/04szw"},
    {"id": 302, "name": "Swim cap", "freebase_id": "/m/04tn4x"},
    {"id": 303, "name": "Frying pan", "freebase_id": "/m/04v6l4"},
    {"id": 304, "name": "Snowplow", "freebase_id": "/m/04vv5k"},
    {"id": 305, "name": "Bathroom cabinet", "freebase_id": "/m/04y4h8h"},
    {"id": 306, "name": "Missile", "freebase_id": "/m/04ylt"},
    {"id": 307, "name": "Bust", "freebase_id": "/m/04yqq2"},
    {"id": 308, "name": "Man", "freebase_id": "/m/04yx4"},
    {"id": 309, "name": "Waffle iron", "freebase_id": "/m/04z4wx"},
    {"id": 310, "name": "Milk", "freebase_id": "/m/04zpv"},
    {"id": 311, "name": "Ring binder", "freebase_id": "/m/04zwwv"},
    {"id": 312, "name": "Plate", "freebase_id": "/m/050gv4"},
    {"id": 313, "name": "Mobile phone", "freebase_id": "/m/050k8"},
    {"id": 314, "name": "Baked goods", "freebase_id": "/m/052lwg6"},
    {"id": 315, "name": "Mushroom", "freebase_id": "/m/052sf"},
    {"id": 316, "name": "Crutch", "freebase_id": "/m/05441v"},
    {"id": 317, "name": "Pitcher (Container)", "freebase_id": "/m/054fyh"},
    {"id": 318, "name": "Mirror", "freebase_id": "/m/054_l"},
    {"id": 319, "name": "Personal flotation device", "freebase_id": "/m/054xkw"},
    {"id": 320, "name": "Table tennis racket", "freebase_id": "/m/05_5p_0"},
    {"id": 321, "name": "Pencil case", "freebase_id": "/m/05676x"},
    {"id": 322, "name": "Musical keyboard", "freebase_id": "/m/057cc"},
    {"id": 323, "name": "Scoreboard", "freebase_id": "/m/057p5t"},
    {"id": 324, "name": "Briefcase", "freebase_id": "/m/0584n8"},
    {"id": 325, "name": "Kitchen knife", "freebase_id": "/m/058qzx"},
    {"id": 326, "name": "Nail (Construction)", "freebase_id": "/m/05bm6"},
    {"id": 327, "name": "Tennis ball", "freebase_id": "/m/05ctyq"},
    {"id": 328, "name": "Plastic bag", "freebase_id": "/m/05gqfk"},
    {"id": 329, "name": "Oboe", "freebase_id": "/m/05kms"},
    {"id": 330, "name": "Chest of drawers", "freebase_id": "/m/05kyg_"},
    {"id": 331, "name": "Ostrich", "freebase_id": "/m/05n4y"},
    {"id": 332, "name": "Piano", "freebase_id": "/m/05r5c"},
    {"id": 333, "name": "Girl", "freebase_id": "/m/05r655"},
    {"id": 334, "name": "Plant", "freebase_id": "/m/05s2s"},
    {"id": 335, "name": "Potato", "freebase_id": "/m/05vtc"},
    {"id": 336, "name": "Hair spray", "freebase_id": "/m/05w9t9"},
    {"id": 337, "name": "Sports equipment", "freebase_id": "/m/05y5lj"},
    {"id": 338, "name": "Pasta", "freebase_id": "/m/05z55"},
    {"id": 339, "name": "Penguin", "freebase_id": "/m/05z6w"},
    {"id": 340, "name": "Pumpkin", "freebase_id": "/m/05zsy"},
    {"id": 341, "name": "Pear", "freebase_id": "/m/061_f"},
    {"id": 342, "name": "Infant bed", "freebase_id": "/m/061hd_"},
    {"id": 343, "name": "Polar bear", "freebase_id": "/m/0633h"},
    {"id": 344, "name": "Mixer", "freebase_id": "/m/063rgb"},
    {"id": 345, "name": "Cupboard", "freebase_id": "/m/0642b4"},
    {"id": 346, "name": "Jacuzzi", "freebase_id": "/m/065h6l"},
    {"id": 347, "name": "Pizza", "freebase_id": "/m/0663v"},
    {"id": 348, "name": "Digital clock", "freebase_id": "/m/06_72j"},
    {"id": 349, "name": "Pig", "freebase_id": "/m/068zj"},
    {"id": 350, "name": "Reptile", "freebase_id": "/m/06bt6"},
    {"id": 351, "name": "Rifle", "freebase_id": "/m/06c54"},
    {"id": 352, "name": "Lipstick", "freebase_id": "/m/06c7f7"},
    {"id": 353, "name": "Skateboard", "freebase_id": "/m/06_fw"},
    {"id": 354, "name": "Raven", "freebase_id": "/m/06j2d"},
    {"id": 355, "name": "High heels", "freebase_id": "/m/06k2mb"},
    {"id": 356, "name": "Red panda", "freebase_id": "/m/06l9r"},
    {"id": 357, "name": "Rose", "freebase_id": "/m/06m11"},
    {"id": 358, "name": "Rabbit", "freebase_id": "/m/06mf6"},
    {"id": 359, "name": "Sculpture", "freebase_id": "/m/06msq"},
    {"id": 360, "name": "Saxophone", "freebase_id": "/m/06ncr"},
    {"id": 361, "name": "Shotgun", "freebase_id": "/m/06nrc"},
    {"id": 362, "name": "Seafood", "freebase_id": "/m/06nwz"},
    {"id": 363, "name": "Submarine sandwich", "freebase_id": "/m/06pcq"},
    {"id": 364, "name": "Snowboard", "freebase_id": "/m/06__v"},
    {"id": 365, "name": "Sword", "freebase_id": "/m/06y5r"},
    {"id": 366, "name": "Picture frame", "freebase_id": "/m/06z37_"},
    {"id": 367, "name": "Sushi", "freebase_id": "/m/07030"},
    {"id": 368, "name": "Loveseat", "freebase_id": "/m/0703r8"},
    {"id": 369, "name": "Ski", "freebase_id": "/m/071p9"},
    {"id": 370, "name": "Squirrel", "freebase_id": "/m/071qp"},
    {"id": 371, "name": "Tripod", "freebase_id": "/m/073bxn"},
    {"id": 372, "name": "Stethoscope", "freebase_id": "/m/073g6"},
    {"id": 373, "name": "Submarine", "freebase_id": "/m/074d1"},
    {"id": 374, "name": "Scorpion", "freebase_id": "/m/0755b"},
    {"id": 375, "name": "Segway", "freebase_id": "/m/076bq"},
    {"id": 376, "name": "Training bench", "freebase_id": "/m/076lb9"},
    {"id": 377, "name": "Snake", "freebase_id": "/m/078jl"},
    {"id": 378, "name": "Coffee table", "freebase_id": "/m/078n6m"},
    {"id": 379, "name": "Skyscraper", "freebase_id": "/m/079cl"},
    {"id": 380, "name": "Sheep", "freebase_id": "/m/07bgp"},
    {"id": 381, "name": "Television", "freebase_id": "/m/07c52"},
    {"id": 382, "name": "Trombone", "freebase_id": "/m/07c6l"},
    {"id": 383, "name": "Tea", "freebase_id": "/m/07clx"},
    {"id": 384, "name": "Tank", "freebase_id": "/m/07cmd"},
    {"id": 385, "name": "Taco", "freebase_id": "/m/07crc"},
    {"id": 386, "name": "Telephone", "freebase_id": "/m/07cx4"},
    {"id": 387, "name": "Torch", "freebase_id": "/m/07dd4"},
    {"id": 388, "name": "Tiger", "freebase_id": "/m/07dm6"},
    {"id": 389, "name": "Strawberry", "freebase_id": "/m/07fbm7"},
    {"id": 390, "name": "Trumpet", "freebase_id": "/m/07gql"},
    {"id": 391, "name": "Tree", "freebase_id": "/m/07j7r"},
    {"id": 392, "name": "Tomato", "freebase_id": "/m/07j87"},
    {"id": 393, "name": "Train", "freebase_id": "/m/07jdr"},
    {"id": 394, "name": "Tool", "freebase_id": "/m/07k1x"},
    {"id": 395, "name": "Picnic basket", "freebase_id": "/m/07kng9"},
    {"id": 396, "name": "Cooking spray", "freebase_id": "/m/07mcwg"},
    {"id": 397, "name": "Trousers", "freebase_id": "/m/07mhn"},
    {"id": 398, "name": "Bowling equipment", "freebase_id": "/m/07pj7bq"},
    {"id": 399, "name": "Football helmet", "freebase_id": "/m/07qxg_"},
    {"id": 400, "name": "Truck", "freebase_id": "/m/07r04"},
    {"id": 401, "name": "Measuring cup", "freebase_id": "/m/07v9_z"},
    {"id": 402, "name": "Coffeemaker", "freebase_id": "/m/07xyvk"},
    {"id": 403, "name": "Violin", "freebase_id": "/m/07y_7"},
    {"id": 404, "name": "Vehicle", "freebase_id": "/m/07yv9"},
    {"id": 405, "name": "Handbag", "freebase_id": "/m/080hkjn"},
    {"id": 406, "name": "Paper cutter", "freebase_id": "/m/080n7g"},
    {"id": 407, "name": "Wine", "freebase_id": "/m/081qc"},
    {"id": 408, "name": "Weapon", "freebase_id": "/m/083kb"},
    {"id": 409, "name": "Wheel", "freebase_id": "/m/083wq"},
    {"id": 410, "name": "Worm", "freebase_id": "/m/084hf"},
    {"id": 411, "name": "Wok", "freebase_id": "/m/084rd"},
    {"id": 412, "name": "Whale", "freebase_id": "/m/084zz"},
    {"id": 413, "name": "Zebra", "freebase_id": "/m/0898b"},
    {"id": 414, "name": "Auto part", "freebase_id": "/m/08dz3q"},
    {"id": 415, "name": "Jug", "freebase_id": "/m/08hvt4"},
    {"id": 416, "name": "Pizza cutter", "freebase_id": "/m/08ks85"},
    {"id": 417, "name": "Cream", "freebase_id": "/m/08p92x"},
    {"id": 418, "name": "Monkey", "freebase_id": "/m/08pbxl"},
    {"id": 419, "name": "Lion", "freebase_id": "/m/096mb"},
    {"id": 420, "name": "Bread", "freebase_id": "/m/09728"},
    {"id": 421, "name": "Platter", "freebase_id": "/m/099ssp"},
    {"id": 422, "name": "Chicken", "freebase_id": "/m/09b5t"},
    {"id": 423, "name": "Eagle", "freebase_id": "/m/09csl"},
    {"id": 424, "name": "Helicopter", "freebase_id": "/m/09ct_"},
    {"id": 425, "name": "Owl", "freebase_id": "/m/09d5_"},
    {"id": 426, "name": "Duck", "freebase_id": "/m/09ddx"},
    {"id": 427, "name": "Turtle", "freebase_id": "/m/09dzg"},
    {"id": 428, "name": "Hippopotamus", "freebase_id": "/m/09f20"},
    {"id": 429, "name": "Crocodile", "freebase_id": "/m/09f_2"},
    {"id": 430, "name": "Toilet", "freebase_id": "/m/09g1w"},
    {"id": 431, "name": "Toilet paper", "freebase_id": "/m/09gtd"},
    {"id": 432, "name": "Squid", "freebase_id": "/m/09gys"},
    {"id": 433, "name": "Clothing", "freebase_id": "/m/09j2d"},
    {"id": 434, "name": "Footwear", "freebase_id": "/m/09j5n"},
    {"id": 435, "name": "Lemon", "freebase_id": "/m/09k_b"},
    {"id": 436, "name": "Spider", "freebase_id": "/m/09kmb"},
    {"id": 437, "name": "Deer", "freebase_id": "/m/09kx5"},
    {"id": 438, "name": "Frog", "freebase_id": "/m/09ld4"},
    {"id": 439, "name": "Banana", "freebase_id": "/m/09qck"},
    {"id": 440, "name": "Rocket", "freebase_id": "/m/09rvcxw"},
    {"id": 441, "name": "Wine glass", "freebase_id": "/m/09tvcd"},
    {"id": 442, "name": "Countertop", "freebase_id": "/m/0b3fp9"},
    {"id": 443, "name": "Tablet computer", "freebase_id": "/m/0bh9flk"},
    {"id": 444, "name": "Waste container", "freebase_id": "/m/0bjyj5"},
    {"id": 445, "name": "Swimming pool", "freebase_id": "/m/0b_rs"},
    {"id": 446, "name": "Dog", "freebase_id": "/m/0bt9lr"},
    {"id": 447, "name": "Book", "freebase_id": "/m/0bt_c3"},
    {"id": 448, "name": "Elephant", "freebase_id": "/m/0bwd_0j"},
    {"id": 449, "name": "Shark", "freebase_id": "/m/0by6g"},
    {"id": 450, "name": "Candle", "freebase_id": "/m/0c06p"},
    {"id": 451, "name": "Leopard", "freebase_id": "/m/0c29q"},
    {"id": 452, "name": "Axe", "freebase_id": "/m/0c2jj"},
    {"id": 453, "name": "Hand dryer", "freebase_id": "/m/0c3m8g"},
    {"id": 454, "name": "Soap dispenser", "freebase_id": "/m/0c3mkw"},
    {"id": 455, "name": "Porcupine", "freebase_id": "/m/0c568"},
    {"id": 456, "name": "Flower", "freebase_id": "/m/0c9ph5"},
    {"id": 457, "name": "Canary", "freebase_id": "/m/0ccs93"},
    {"id": 458, "name": "Cheetah", "freebase_id": "/m/0cd4d"},
    {"id": 459, "name": "Palm tree", "freebase_id": "/m/0cdl1"},
    {"id": 460, "name": "Hamburger", "freebase_id": "/m/0cdn1"},
    {"id": 461, "name": "Maple", "freebase_id": "/m/0cffdh"},
    {"id": 462, "name": "Building", "freebase_id": "/m/0cgh4"},
    {"id": 463, "name": "Fish", "freebase_id": "/m/0ch_cf"},
    {"id": 464, "name": "Lobster", "freebase_id": "/m/0cjq5"},
    {"id": 465, "name": "Garden Asparagus", "freebase_id": "/m/0cjs7"},
    {"id": 466, "name": "Furniture", "freebase_id": "/m/0c_jw"},
    {"id": 467, "name": "Hedgehog", "freebase_id": "/m/0cl4p"},
    {"id": 468, "name": "Airplane", "freebase_id": "/m/0cmf2"},
    {"id": 469, "name": "Spoon", "freebase_id": "/m/0cmx8"},
    {"id": 470, "name": "Otter", "freebase_id": "/m/0cn6p"},
    {"id": 471, "name": "Bull", "freebase_id": "/m/0cnyhnx"},
    {"id": 472, "name": "Oyster", "freebase_id": "/m/0_cp5"},
    {"id": 473, "name": "Horizontal bar", "freebase_id": "/m/0cqn2"},
    {"id": 474, "name": "Convenience store", "freebase_id": "/m/0crjs"},
    {"id": 475, "name": "Bomb", "freebase_id": "/m/0ct4f"},
    {"id": 476, "name": "Bench", "freebase_id": "/m/0cvnqh"},
    {"id": 477, "name": "Ice cream", "freebase_id": "/m/0cxn2"},
    {"id": 478, "name": "Caterpillar", "freebase_id": "/m/0cydv"},
    {"id": 479, "name": "Butterfly", "freebase_id": "/m/0cyf8"},
    {"id": 480, "name": "Parachute", "freebase_id": "/m/0cyfs"},
    {"id": 481, "name": "Orange", "freebase_id": "/m/0cyhj_"},
    {"id": 482, "name": "Antelope", "freebase_id": "/m/0czz2"},
    {"id": 483, "name": "Beaker", "freebase_id": "/m/0d20w4"},
    {"id": 484, "name": "Moths and butterflies", "freebase_id": "/m/0d_2m"},
    {"id": 485, "name": "Window", "freebase_id": "/m/0d4v4"},
    {"id": 486, "name": "Closet", "freebase_id": "/m/0d4w1"},
    {"id": 487, "name": "Castle", "freebase_id": "/m/0d5gx"},
    {"id": 488, "name": "Jellyfish", "freebase_id": "/m/0d8zb"},
    {"id": 489, "name": "Goose", "freebase_id": "/m/0dbvp"},
    {"id": 490, "name": "Mule", "freebase_id": "/m/0dbzx"},
    {"id": 491, "name": "Swan", "freebase_id": "/m/0dftk"},
    {"id": 492, "name": "Peach", "freebase_id": "/m/0dj6p"},
    {"id": 493, "name": "Coconut", "freebase_id": "/m/0djtd"},
    {"id": 494, "name": "Seat belt", "freebase_id": "/m/0dkzw"},
    {"id": 495, "name": "Raccoon", "freebase_id": "/m/0dq75"},
    {"id": 496, "name": "Chisel", "freebase_id": "/m/0_dqb"},
    {"id": 497, "name": "Fork", "freebase_id": "/m/0dt3t"},
    {"id": 498, "name": "Lamp", "freebase_id": "/m/0dtln"},
    {"id": 499, "name": "Camera", "freebase_id": "/m/0dv5r"},
    {"id": 500, "name": "Squash (Plant)", "freebase_id": "/m/0dv77"},
    {"id": 501, "name": "Racket", "freebase_id": "/m/0dv9c"},
    {"id": 502, "name": "Human face", "freebase_id": "/m/0dzct"},
    {"id": 503, "name": "Human arm", "freebase_id": "/m/0dzf4"},
    {"id": 504, "name": "Vegetable", "freebase_id": "/m/0f4s2w"},
    {"id": 505, "name": "Diaper", "freebase_id": "/m/0f571"},
    {"id": 506, "name": "Unicycle", "freebase_id": "/m/0f6nr"},
    {"id": 507, "name": "Falcon", "freebase_id": "/m/0f6wt"},
    {"id": 508, "name": "Chime", "freebase_id": "/m/0f8s22"},
    {"id": 509, "name": "Snail", "freebase_id": "/m/0f9_l"},
    {"id": 510, "name": "Shellfish", "freebase_id": "/m/0fbdv"},
    {"id": 511, "name": "Cabbage", "freebase_id": "/m/0fbw6"},
    {"id": 512, "name": "Carrot", "freebase_id": "/m/0fj52s"},
    {"id": 513, "name": "Mango", "freebase_id": "/m/0fldg"},
    {"id": 514, "name": "Jeans", "freebase_id": "/m/0fly7"},
    {"id": 515, "name": "Flowerpot", "freebase_id": "/m/0fm3zh"},
    {"id": 516, "name": "Pineapple", "freebase_id": "/m/0fp6w"},
    {"id": 517, "name": "Drawer", "freebase_id": "/m/0fqfqc"},
    {"id": 518, "name": "Stool", "freebase_id": "/m/0fqt361"},
    {"id": 519, "name": "Envelope", "freebase_id": "/m/0frqm"},
    {"id": 520, "name": "Cake", "freebase_id": "/m/0fszt"},
    {"id": 521, "name": "Dragonfly", "freebase_id": "/m/0ft9s"},
    {"id": 522, "name": "Common sunflower", "freebase_id": "/m/0ftb8"},
    {"id": 523, "name": "Microwave oven", "freebase_id": "/m/0fx9l"},
    {"id": 524, "name": "Honeycomb", "freebase_id": "/m/0fz0h"},
    {"id": 525, "name": "Marine mammal", "freebase_id": "/m/0gd2v"},
    {"id": 526, "name": "Sea lion", "freebase_id": "/m/0gd36"},
    {"id": 527, "name": "Ladybug", "freebase_id": "/m/0gj37"},
    {"id": 528, "name": "Shelf", "freebase_id": "/m/0gjbg72"},
    {"id": 529, "name": "Watch", "freebase_id": "/m/0gjkl"},
    {"id": 530, "name": "Candy", "freebase_id": "/m/0gm28"},
    {"id": 531, "name": "Salad", "freebase_id": "/m/0grw1"},
    {"id": 532, "name": "Parrot", "freebase_id": "/m/0gv1x"},
    {"id": 533, "name": "Handgun", "freebase_id": "/m/0gxl3"},
    {"id": 534, "name": "Sparrow", "freebase_id": "/m/0h23m"},
    {"id": 535, "name": "Van", "freebase_id": "/m/0h2r6"},
    {"id": 536, "name": "Grinder", "freebase_id": "/m/0h8jyh6"},
    {"id": 537, "name": "Spice rack", "freebase_id": "/m/0h8kx63"},
    {"id": 538, "name": "Light bulb", "freebase_id": "/m/0h8l4fh"},
    {"id": 539, "name": "Corded phone", "freebase_id": "/m/0h8lkj8"},
    {"id": 540, "name": "Sports uniform", "freebase_id": "/m/0h8mhzd"},
    {"id": 541, "name": "Tennis racket", "freebase_id": "/m/0h8my_4"},
    {"id": 542, "name": "Wall clock", "freebase_id": "/m/0h8mzrc"},
    {"id": 543, "name": "Serving tray", "freebase_id": "/m/0h8n27j"},
    {"id": 544, "name": "Kitchen & dining room table", "freebase_id": "/m/0h8n5zk"},
    {"id": 545, "name": "Dog bed", "freebase_id": "/m/0h8n6f9"},
    {"id": 546, "name": "Cake stand", "freebase_id": "/m/0h8n6ft"},
    {"id": 547, "name": "Cat furniture", "freebase_id": "/m/0h8nm9j"},
    {"id": 548, "name": "Bathroom accessory", "freebase_id": "/m/0h8nr_l"},
    {"id": 549, "name": "Facial tissue holder", "freebase_id": "/m/0h8nsvg"},
    {"id": 550, "name": "Pressure cooker", "freebase_id": "/m/0h8ntjv"},
    {"id": 551, "name": "Kitchen appliance", "freebase_id": "/m/0h99cwc"},
    {"id": 552, "name": "Tire", "freebase_id": "/m/0h9mv"},
    {"id": 553, "name": "Ruler", "freebase_id": "/m/0hdln"},
    {"id": 554, "name": "Luggage and bags", "freebase_id": "/m/0hf58v5"},
    {"id": 555, "name": "Microphone", "freebase_id": "/m/0hg7b"},
    {"id": 556, "name": "Broccoli", "freebase_id": "/m/0hkxq"},
    {"id": 557, "name": "Umbrella", "freebase_id": "/m/0hnnb"},
    {"id": 558, "name": "Pastry", "freebase_id": "/m/0hnyx"},
    {"id": 559, "name": "Grapefruit", "freebase_id": "/m/0hqkz"},
    {"id": 560, "name": "Band-aid", "freebase_id": "/m/0j496"},
    {"id": 561, "name": "Animal", "freebase_id": "/m/0jbk"},
    {"id": 562, "name": "Bell pepper", "freebase_id": "/m/0jg57"},
    {"id": 563, "name": "Turkey", "freebase_id": "/m/0jly1"},
    {"id": 564, "name": "Lily", "freebase_id": "/m/0jqgx"},
    {"id": 565, "name": "Pomegranate", "freebase_id": "/m/0jwn_"},
    {"id": 566, "name": "Doughnut", "freebase_id": "/m/0jy4k"},
    {"id": 567, "name": "Glasses", "freebase_id": "/m/0jyfg"},
    {"id": 568, "name": "Human nose", "freebase_id": "/m/0k0pj"},
    {"id": 569, "name": "Pen", "freebase_id": "/m/0k1tl"},
    {"id": 570, "name": "Ant", "freebase_id": "/m/0_k2"},
    {"id": 571, "name": "Car", "freebase_id": "/m/0k4j"},
    {"id": 572, "name": "Aircraft", "freebase_id": "/m/0k5j"},
    {"id": 573, "name": "Human hand", "freebase_id": "/m/0k65p"},
    {"id": 574, "name": "Skunk", "freebase_id": "/m/0km7z"},
    {"id": 575, "name": "Teddy bear", "freebase_id": "/m/0kmg4"},
    {"id": 576, "name": "Watermelon", "freebase_id": "/m/0kpqd"},
    {"id": 577, "name": "Cantaloupe", "freebase_id": "/m/0kpt_"},
    {"id": 578, "name": "Dishwasher", "freebase_id": "/m/0ky7b"},
    {"id": 579, "name": "Flute", "freebase_id": "/m/0l14j_"},
    {"id": 580, "name": "Balance beam", "freebase_id": "/m/0l3ms"},
    {"id": 581, "name": "Sandwich", "freebase_id": "/m/0l515"},
    {"id": 582, "name": "Shrimp", "freebase_id": "/m/0ll1f78"},
    {"id": 583, "name": "Sewing machine", "freebase_id": "/m/0llzx"},
    {"id": 584, "name": "Binoculars", "freebase_id": "/m/0lt4_"},
    {"id": 585, "name": "Rays and skates", "freebase_id": "/m/0m53l"},
    {"id": 586, "name": "Ipod", "freebase_id": "/m/0mcx2"},
    {"id": 587, "name": "Accordion", "freebase_id": "/m/0mkg"},
    {"id": 588, "name": "Willow", "freebase_id": "/m/0mw_6"},
    {"id": 589, "name": "Crab", "freebase_id": "/m/0n28_"},
    {"id": 590, "name": "Crown", "freebase_id": "/m/0nl46"},
    {"id": 591, "name": "Seahorse", "freebase_id": "/m/0nybt"},
    {"id": 592, "name": "Perfume", "freebase_id": "/m/0p833"},
    {"id": 593, "name": "Alpaca", "freebase_id": "/m/0pcr"},
    {"id": 594, "name": "Taxi", "freebase_id": "/m/0pg52"},
    {"id": 595, "name": "Canoe", "freebase_id": "/m/0ph39"},
    {"id": 596, "name": "Remote control", "freebase_id": "/m/0qjjc"},
    {"id": 597, "name": "Wheelchair", "freebase_id": "/m/0qmmr"},
    {"id": 598, "name": "Rugby ball", "freebase_id": "/m/0wdt60w"},
    {"id": 599, "name": "Armadillo", "freebase_id": "/m/0xfy"},
    {"id": 600, "name": "Maracas", "freebase_id": "/m/0xzly"},
    {"id": 601, "name": "Helmet", "freebase_id": "/m/0zvk5"},
]

categories_seg = [
    {"id": 1, "name": "Screwdriver", "freebase_id": "/m/01bms0"},
    {"id": 2, "name": "Light switch", "freebase_id": "/m/03jbxj"},
    {"id": 3, "name": "Doughnut", "freebase_id": "/m/0jy4k"},
    {"id": 4, "name": "Toilet paper", "freebase_id": "/m/09gtd"},
    {"id": 5, "name": "Wrench", "freebase_id": "/m/01j5ks"},
    {"id": 6, "name": "Toaster", "freebase_id": "/m/01k6s3"},
    {"id": 7, "name": "Tennis ball", "freebase_id": "/m/05ctyq"},
    {"id": 8, "name": "Radish", "freebase_id": "/m/015x5n"},
    {"id": 9, "name": "Pomegranate", "freebase_id": "/m/0jwn_"},
    {"id": 10, "name": "Kite", "freebase_id": "/m/02zt3"},
    {"id": 11, "name": "Table tennis racket", "freebase_id": "/m/05_5p_0"},
    {"id": 12, "name": "Hamster", "freebase_id": "/m/03qrc"},
    {"id": 13, "name": "Barge", "freebase_id": "/m/01btn"},
    {"id": 14, "name": "Shower", "freebase_id": "/m/02f9f_"},
    {"id": 15, "name": "Printer", "freebase_id": "/m/01m4t"},
    {"id": 16, "name": "Snowmobile", "freebase_id": "/m/01x3jk"},
    {"id": 17, "name": "Fire hydrant", "freebase_id": "/m/01pns0"},
    {"id": 18, "name": "Limousine", "freebase_id": "/m/01lcw4"},
    {"id": 19, "name": "Whale", "freebase_id": "/m/084zz"},
    {"id": 20, "name": "Microwave oven", "freebase_id": "/m/0fx9l"},
    {"id": 21, "name": "Asparagus", "freebase_id": "/m/0cjs7"},
    {"id": 22, "name": "Lion", "freebase_id": "/m/096mb"},
    {"id": 23, "name": "Spatula", "freebase_id": "/m/02d1br"},
    {"id": 24, "name": "Torch", "freebase_id": "/m/07dd4"},
    {"id": 25, "name": "Volleyball", "freebase_id": "/m/02rgn06"},
    {"id": 26, "name": "Ambulance", "freebase_id": "/m/012n7d"},
    {"id": 27, "name": "Chopsticks", "freebase_id": "/m/01_5g"},
    {"id": 28, "name": "Raccoon", "freebase_id": "/m/0dq75"},
    {"id": 29, "name": "Blue jay", "freebase_id": "/m/01f8m5"},
    {"id": 30, "name": "Lynx", "freebase_id": "/m/04g2r"},
    {"id": 31, "name": "Dice", "freebase_id": "/m/029b3"},
    {"id": 32, "name": "Filing cabinet", "freebase_id": "/m/047j0r"},
    {"id": 33, "name": "Ruler", "freebase_id": "/m/0hdln"},
    {"id": 34, "name": "Power plugs and sockets", "freebase_id": "/m/03bbps"},
    {"id": 35, "name": "Bell pepper", "freebase_id": "/m/0jg57"},
    {"id": 36, "name": "Binoculars", "freebase_id": "/m/0lt4_"},
    {"id": 37, "name": "Pretzel", "freebase_id": "/m/01f91_"},
    {"id": 38, "name": "Hot dog", "freebase_id": "/m/01b9xk"},
    {"id": 39, "name": "Missile", "freebase_id": "/m/04ylt"},
    {"id": 40, "name": "Common fig", "freebase_id": "/m/043nyj"},
    {"id": 41, "name": "Croissant", "freebase_id": "/m/015wgc"},
    {"id": 42, "name": "Adhesive tape", "freebase_id": "/m/03m3vtv"},
    {"id": 43, "name": "Slow cooker", "freebase_id": "/m/02tsc9"},
    {"id": 44, "name": "Dog bed", "freebase_id": "/m/0h8n6f9"},
    {"id": 45, "name": "Harpsichord", "freebase_id": "/m/03q5t"},
    {"id": 46, "name": "Billiard table", "freebase_id": "/m/04p0qw"},
    {"id": 47, "name": "Alpaca", "freebase_id": "/m/0pcr"},
    {"id": 48, "name": "Harbor seal", "freebase_id": "/m/02l8p9"},
    {"id": 49, "name": "Grape", "freebase_id": "/m/0388q"},
    {"id": 50, "name": "Nail", "freebase_id": "/m/05bm6"},
    {"id": 51, "name": "Paper towel", "freebase_id": "/m/02w3r3"},
    {"id": 52, "name": "Alarm clock", "freebase_id": "/m/046dlr"},
    {"id": 53, "name": "Guacamole", "freebase_id": "/m/02g30s"},
    {"id": 54, "name": "Starfish", "freebase_id": "/m/01h8tj"},
    {"id": 55, "name": "Zebra", "freebase_id": "/m/0898b"},
    {"id": 56, "name": "Segway", "freebase_id": "/m/076bq"},
    {"id": 57, "name": "Sea turtle", "freebase_id": "/m/0120dh"},
    {"id": 58, "name": "Scissors", "freebase_id": "/m/01lsmm"},
    {"id": 59, "name": "Rhinoceros", "freebase_id": "/m/03d443"},
    {"id": 60, "name": "Kangaroo", "freebase_id": "/m/04c0y"},
    {"id": 61, "name": "Jaguar", "freebase_id": "/m/0449p"},
    {"id": 62, "name": "Leopard", "freebase_id": "/m/0c29q"},
    {"id": 63, "name": "Dumbbell", "freebase_id": "/m/04h8sr"},
    {"id": 64, "name": "Envelope", "freebase_id": "/m/0frqm"},
    {"id": 65, "name": "Winter melon", "freebase_id": "/m/02cvgx"},
    {"id": 66, "name": "Teapot", "freebase_id": "/m/01fh4r"},
    {"id": 67, "name": "Camel", "freebase_id": "/m/01x_v"},
    {"id": 68, "name": "Beaker", "freebase_id": "/m/0d20w4"},
    {"id": 69, "name": "Brown bear", "freebase_id": "/m/01dxs"},
    {"id": 70, "name": "Toilet", "freebase_id": "/m/09g1w"},
    {"id": 71, "name": "Teddy bear", "freebase_id": "/m/0kmg4"},
    {"id": 72, "name": "Briefcase", "freebase_id": "/m/0584n8"},
    {"id": 73, "name": "Stop sign", "freebase_id": "/m/02pv19"},
    {"id": 74, "name": "Tiger", "freebase_id": "/m/07dm6"},
    {"id": 75, "name": "Cabbage", "freebase_id": "/m/0fbw6"},
    {"id": 76, "name": "Giraffe", "freebase_id": "/m/03bk1"},
    {"id": 77, "name": "Polar bear", "freebase_id": "/m/0633h"},
    {"id": 78, "name": "Shark", "freebase_id": "/m/0by6g"},
    {"id": 79, "name": "Rabbit", "freebase_id": "/m/06mf6"},
    {"id": 80, "name": "Swim cap", "freebase_id": "/m/04tn4x"},
    {"id": 81, "name": "Pressure cooker", "freebase_id": "/m/0h8ntjv"},
    {"id": 82, "name": "Kitchen knife", "freebase_id": "/m/058qzx"},
    {"id": 83, "name": "Submarine sandwich", "freebase_id": "/m/06pcq"},
    {"id": 84, "name": "Flashlight", "freebase_id": "/m/01kb5b"},
    {"id": 85, "name": "Penguin", "freebase_id": "/m/05z6w"},
    {"id": 86, "name": "Snake", "freebase_id": "/m/078jl"},
    {"id": 87, "name": "Zucchini", "freebase_id": "/m/027pcv"},
    {"id": 88, "name": "Bat", "freebase_id": "/m/01h44"},
    {"id": 89, "name": "Food processor", "freebase_id": "/m/03y6mg"},
    {"id": 90, "name": "Ostrich", "freebase_id": "/m/05n4y"},
    {"id": 91, "name": "Sea lion", "freebase_id": "/m/0gd36"},
    {"id": 92, "name": "Goldfish", "freebase_id": "/m/03fj2"},
    {"id": 93, "name": "Elephant", "freebase_id": "/m/0bwd_0j"},
    {"id": 94, "name": "Rocket", "freebase_id": "/m/09rvcxw"},
    {"id": 95, "name": "Mouse", "freebase_id": "/m/04rmv"},
    {"id": 96, "name": "Oyster", "freebase_id": "/m/0_cp5"},
    {"id": 97, "name": "Digital clock", "freebase_id": "/m/06_72j"},
    {"id": 98, "name": "Otter", "freebase_id": "/m/0cn6p"},
    {"id": 99, "name": "Dolphin", "freebase_id": "/m/02hj4"},
    {"id": 100, "name": "Punching bag", "freebase_id": "/m/0420v5"},
    {"id": 101, "name": "Corded phone", "freebase_id": "/m/0h8lkj8"},
    {"id": 102, "name": "Tennis racket", "freebase_id": "/m/0h8my_4"},
    {"id": 103, "name": "Pancake", "freebase_id": "/m/01dwwc"},
    {"id": 104, "name": "Mango", "freebase_id": "/m/0fldg"},
    {"id": 105, "name": "Crocodile", "freebase_id": "/m/09f_2"},
    {"id": 106, "name": "Waffle", "freebase_id": "/m/01dwsz"},
    {"id": 107, "name": "Computer mouse", "freebase_id": "/m/020lf"},
    {"id": 108, "name": "Kettle", "freebase_id": "/m/03s_tn"},
    {"id": 109, "name": "Tart", "freebase_id": "/m/02zvsm"},
    {"id": 110, "name": "Oven", "freebase_id": "/m/029bxz"},
    {"id": 111, "name": "Banana", "freebase_id": "/m/09qck"},
    {"id": 112, "name": "Cheetah", "freebase_id": "/m/0cd4d"},
    {"id": 113, "name": "Raven", "freebase_id": "/m/06j2d"},
    {"id": 114, "name": "Frying pan", "freebase_id": "/m/04v6l4"},
    {"id": 115, "name": "Pear", "freebase_id": "/m/061_f"},
    {"id": 116, "name": "Fox", "freebase_id": "/m/0306r"},
    {"id": 117, "name": "Skateboard", "freebase_id": "/m/06_fw"},
    {"id": 118, "name": "Rugby ball", "freebase_id": "/m/0wdt60w"},
    {"id": 119, "name": "Watermelon", "freebase_id": "/m/0kpqd"},
    {"id": 120, "name": "Flute", "freebase_id": "/m/0l14j_"},
    {"id": 121, "name": "Canary", "freebase_id": "/m/0ccs93"},
    {"id": 122, "name": "Door handle", "freebase_id": "/m/03c7gz"},
    {"id": 123, "name": "Saxophone", "freebase_id": "/m/06ncr"},
    {"id": 124, "name": "Burrito", "freebase_id": "/m/01j3zr"},
    {"id": 125, "name": "Suitcase", "freebase_id": "/m/01s55n"},
    {"id": 126, "name": "Roller skates", "freebase_id": "/m/02p3w7d"},
    {"id": 127, "name": "Dagger", "freebase_id": "/m/02gzp"},
    {"id": 128, "name": "Seat belt", "freebase_id": "/m/0dkzw"},
    {"id": 129, "name": "Washing machine", "freebase_id": "/m/0174k2"},
    {"id": 130, "name": "Jet ski", "freebase_id": "/m/01xs3r"},
    {"id": 131, "name": "Sombrero", "freebase_id": "/m/02jfl0"},
    {"id": 132, "name": "Pig", "freebase_id": "/m/068zj"},
    {"id": 133, "name": "Drinking straw", "freebase_id": "/m/03v5tg"},
    {"id": 134, "name": "Peach", "freebase_id": "/m/0dj6p"},
    {"id": 135, "name": "Tortoise", "freebase_id": "/m/011k07"},
    {"id": 136, "name": "Towel", "freebase_id": "/m/0162_1"},
    {"id": 137, "name": "Tablet computer", "freebase_id": "/m/0bh9flk"},
    {"id": 138, "name": "Cucumber", "freebase_id": "/m/015x4r"},
    {"id": 139, "name": "Mule", "freebase_id": "/m/0dbzx"},
    {"id": 140, "name": "Potato", "freebase_id": "/m/05vtc"},
    {"id": 141, "name": "Frog", "freebase_id": "/m/09ld4"},
    {"id": 142, "name": "Bear", "freebase_id": "/m/01dws"},
    {"id": 143, "name": "Lighthouse", "freebase_id": "/m/04h7h"},
    {"id": 144, "name": "Belt", "freebase_id": "/m/0176mf"},
    {"id": 145, "name": "Baseball bat", "freebase_id": "/m/03g8mr"},
    {"id": 146, "name": "Racket", "freebase_id": "/m/0dv9c"},
    {"id": 147, "name": "Sword", "freebase_id": "/m/06y5r"},
    {"id": 148, "name": "Bagel", "freebase_id": "/m/01fb_0"},
    {"id": 149, "name": "Goat", "freebase_id": "/m/03fwl"},
    {"id": 150, "name": "Lizard", "freebase_id": "/m/04m9y"},
    {"id": 151, "name": "Parrot", "freebase_id": "/m/0gv1x"},
    {"id": 152, "name": "Owl", "freebase_id": "/m/09d5_"},
    {"id": 153, "name": "Turkey", "freebase_id": "/m/0jly1"},
    {"id": 154, "name": "Cello", "freebase_id": "/m/01xqw"},
    {"id": 155, "name": "Knife", "freebase_id": "/m/04ctx"},
    {"id": 156, "name": "Handgun", "freebase_id": "/m/0gxl3"},
    {"id": 157, "name": "Carrot", "freebase_id": "/m/0fj52s"},
    {"id": 158, "name": "Hamburger", "freebase_id": "/m/0cdn1"},
    {"id": 159, "name": "Grapefruit", "freebase_id": "/m/0hqkz"},
    {"id": 160, "name": "Tap", "freebase_id": "/m/02jz0l"},
    {"id": 161, "name": "Tea", "freebase_id": "/m/07clx"},
    {"id": 162, "name": "Bull", "freebase_id": "/m/0cnyhnx"},
    {"id": 163, "name": "Turtle", "freebase_id": "/m/09dzg"},
    {"id": 164, "name": "Bust", "freebase_id": "/m/04yqq2"},
    {"id": 165, "name": "Monkey", "freebase_id": "/m/08pbxl"},
    {"id": 166, "name": "Wok", "freebase_id": "/m/084rd"},
    {"id": 167, "name": "Broccoli", "freebase_id": "/m/0hkxq"},
    {"id": 168, "name": "Pitcher", "freebase_id": "/m/054fyh"},
    {"id": 169, "name": "Whiteboard", "freebase_id": "/m/02d9qx"},
    {"id": 170, "name": "Squirrel", "freebase_id": "/m/071qp"},
    {"id": 171, "name": "Jug", "freebase_id": "/m/08hvt4"},
    {"id": 172, "name": "Woodpecker", "freebase_id": "/m/01dy8n"},
    {"id": 173, "name": "Pizza", "freebase_id": "/m/0663v"},
    {"id": 174, "name": "Surfboard", "freebase_id": "/m/019w40"},
    {"id": 175, "name": "Sofa bed", "freebase_id": "/m/03m3pdh"},
    {"id": 176, "name": "Sheep", "freebase_id": "/m/07bgp"},
    {"id": 177, "name": "Candle", "freebase_id": "/m/0c06p"},
    {"id": 178, "name": "Muffin", "freebase_id": "/m/01tcjp"},
    {"id": 179, "name": "Cookie", "freebase_id": "/m/021mn"},
    {"id": 180, "name": "Apple", "freebase_id": "/m/014j1m"},
    {"id": 181, "name": "Chest of drawers", "freebase_id": "/m/05kyg_"},
    {"id": 182, "name": "Skull", "freebase_id": "/m/016m2d"},
    {"id": 183, "name": "Chicken", "freebase_id": "/m/09b5t"},
    {"id": 184, "name": "Loveseat", "freebase_id": "/m/0703r8"},
    {"id": 185, "name": "Baseball glove", "freebase_id": "/m/03grzl"},
    {"id": 186, "name": "Piano", "freebase_id": "/m/05r5c"},
    {"id": 187, "name": "Waste container", "freebase_id": "/m/0bjyj5"},
    {"id": 188, "name": "Barrel", "freebase_id": "/m/02zn6n"},
    {"id": 189, "name": "Swan", "freebase_id": "/m/0dftk"},
    {"id": 190, "name": "Taxi", "freebase_id": "/m/0pg52"},
    {"id": 191, "name": "Lemon", "freebase_id": "/m/09k_b"},
    {"id": 192, "name": "Pumpkin", "freebase_id": "/m/05zsy"},
    {"id": 193, "name": "Sparrow", "freebase_id": "/m/0h23m"},
    {"id": 194, "name": "Orange", "freebase_id": "/m/0cyhj_"},
    {"id": 195, "name": "Tank", "freebase_id": "/m/07cmd"},
    {"id": 196, "name": "Sandwich", "freebase_id": "/m/0l515"},
    {"id": 197, "name": "Coffee", "freebase_id": "/m/02vqfm"},
    {"id": 198, "name": "Juice", "freebase_id": "/m/01z1kdw"},
    {"id": 199, "name": "Coin", "freebase_id": "/m/0242l"},
    {"id": 200, "name": "Pen", "freebase_id": "/m/0k1tl"},
    {"id": 201, "name": "Watch", "freebase_id": "/m/0gjkl"},
    {"id": 202, "name": "Eagle", "freebase_id": "/m/09csl"},
    {"id": 203, "name": "Goose", "freebase_id": "/m/0dbvp"},
    {"id": 204, "name": "Falcon", "freebase_id": "/m/0f6wt"},
    {"id": 205, "name": "Christmas tree", "freebase_id": "/m/025nd"},
    {"id": 206, "name": "Sunflower", "freebase_id": "/m/0ftb8"},
    {"id": 207, "name": "Vase", "freebase_id": "/m/02s195"},
    {"id": 208, "name": "Football", "freebase_id": "/m/01226z"},
    {"id": 209, "name": "Canoe", "freebase_id": "/m/0ph39"},
    {"id": 210, "name": "High heels", "freebase_id": "/m/06k2mb"},
    {"id": 211, "name": "Spoon", "freebase_id": "/m/0cmx8"},
    {"id": 212, "name": "Mug", "freebase_id": "/m/02jvh9"},
    {"id": 213, "name": "Swimwear", "freebase_id": "/m/01gkx_"},
    {"id": 214, "name": "Duck", "freebase_id": "/m/09ddx"},
    {"id": 215, "name": "Cat", "freebase_id": "/m/01yrx"},
    {"id": 216, "name": "Tomato", "freebase_id": "/m/07j87"},
    {"id": 217, "name": "Cocktail", "freebase_id": "/m/024g6"},
    {"id": 218, "name": "Clock", "freebase_id": "/m/01x3z"},
    {"id": 219, "name": "Cowboy hat", "freebase_id": "/m/025rp__"},
    {"id": 220, "name": "Miniskirt", "freebase_id": "/m/01cmb2"},
    {"id": 221, "name": "Cattle", "freebase_id": "/m/01xq0k1"},
    {"id": 222, "name": "Strawberry", "freebase_id": "/m/07fbm7"},
    {"id": 223, "name": "Bronze sculpture", "freebase_id": "/m/01yx86"},
    {"id": 224, "name": "Pillow", "freebase_id": "/m/034c16"},
    {"id": 225, "name": "Squash", "freebase_id": "/m/0dv77"},
    {"id": 226, "name": "Traffic light", "freebase_id": "/m/015qff"},
    {"id": 227, "name": "Saucer", "freebase_id": "/m/03q5c7"},
    {"id": 228, "name": "Reptile", "freebase_id": "/m/06bt6"},
    {"id": 229, "name": "Cake", "freebase_id": "/m/0fszt"},
    {"id": 230, "name": "Plastic bag", "freebase_id": "/m/05gqfk"},
    {"id": 231, "name": "Studio couch", "freebase_id": "/m/026qbn5"},
    {"id": 232, "name": "Beer", "freebase_id": "/m/01599"},
    {"id": 233, "name": "Scarf", "freebase_id": "/m/02h19r"},
    {"id": 234, "name": "Coffee cup", "freebase_id": "/m/02p5f1q"},
    {"id": 235, "name": "Wine", "freebase_id": "/m/081qc"},
    {"id": 236, "name": "Mushroom", "freebase_id": "/m/052sf"},
    {"id": 237, "name": "Traffic sign", "freebase_id": "/m/01mqdt"},
    {"id": 238, "name": "Camera", "freebase_id": "/m/0dv5r"},
    {"id": 239, "name": "Rose", "freebase_id": "/m/06m11"},
    {"id": 240, "name": "Couch", "freebase_id": "/m/02crq1"},
    {"id": 241, "name": "Handbag", "freebase_id": "/m/080hkjn"},
    {"id": 242, "name": "Fedora", "freebase_id": "/m/02fq_6"},
    {"id": 243, "name": "Sock", "freebase_id": "/m/01nq26"},
    {"id": 244, "name": "Computer keyboard", "freebase_id": "/m/01m2v"},
    {"id": 245, "name": "Mobile phone", "freebase_id": "/m/050k8"},
    {"id": 246, "name": "Ball", "freebase_id": "/m/018xm"},
    {"id": 247, "name": "Balloon", "freebase_id": "/m/01j51"},
    {"id": 248, "name": "Horse", "freebase_id": "/m/03k3r"},
    {"id": 249, "name": "Boot", "freebase_id": "/m/01b638"},
    {"id": 250, "name": "Fish", "freebase_id": "/m/0ch_cf"},
    {"id": 251, "name": "Backpack", "freebase_id": "/m/01940j"},
    {"id": 252, "name": "Skirt", "freebase_id": "/m/02wv6h6"},
    {"id": 253, "name": "Van", "freebase_id": "/m/0h2r6"},
    {"id": 254, "name": "Bread", "freebase_id": "/m/09728"},
    {"id": 255, "name": "Glove", "freebase_id": "/m/0174n1"},
    {"id": 256, "name": "Dog", "freebase_id": "/m/0bt9lr"},
    {"id": 257, "name": "Airplane", "freebase_id": "/m/0cmf2"},
    {"id": 258, "name": "Motorcycle", "freebase_id": "/m/04_sv"},
    {"id": 259, "name": "Drink", "freebase_id": "/m/0271t"},
    {"id": 260, "name": "Book", "freebase_id": "/m/0bt_c3"},
    {"id": 261, "name": "Train", "freebase_id": "/m/07jdr"},
    {"id": 262, "name": "Flower", "freebase_id": "/m/0c9ph5"},
    {"id": 263, "name": "Carnivore", "freebase_id": "/m/01lrl"},
    {"id": 264, "name": "Human ear", "freebase_id": "/m/039xj_"},
    {"id": 265, "name": "Toy", "freebase_id": "/m/0138tl"},
    {"id": 266, "name": "Box", "freebase_id": "/m/025dyy"},
    {"id": 267, "name": "Truck", "freebase_id": "/m/07r04"},
    {"id": 268, "name": "Wheel", "freebase_id": "/m/083wq"},
    {"id": 269, "name": "Aircraft", "freebase_id": "/m/0k5j"},
    {"id": 270, "name": "Bus", "freebase_id": "/m/01bjv"},
    {"id": 271, "name": "Human mouth", "freebase_id": "/m/0283dt1"},
    {"id": 272, "name": "Sculpture", "freebase_id": "/m/06msq"},
    {"id": 273, "name": "Shirt", "freebase_id": "/m/01n4qj"},
    {"id": 274, "name": "Hat", "freebase_id": "/m/02dl1y"},
    {"id": 275, "name": "Vehicle registration plate", "freebase_id": "/m/01jfm_"},
    {"id": 276, "name": "Guitar", "freebase_id": "/m/0342h"},
    {"id": 277, "name": "Sun hat", "freebase_id": "/m/02wbtzl"},
    {"id": 278, "name": "Bottle", "freebase_id": "/m/04dr76w"},
    {"id": 279, "name": "Luggage and bags", "freebase_id": "/m/0hf58v5"},
    {"id": 280, "name": "Trousers", "freebase_id": "/m/07mhn"},
    {"id": 281, "name": "Bicycle wheel", "freebase_id": "/m/01bqk0"},
    {"id": 282, "name": "Suit", "freebase_id": "/m/01xyhv"},
    {"id": 283, "name": "Bowl", "freebase_id": "/m/04kkgm"},
    {"id": 284, "name": "Man", "freebase_id": "/m/04yx4"},
    {"id": 285, "name": "Flowerpot", "freebase_id": "/m/0fm3zh"},
    {"id": 286, "name": "Laptop", "freebase_id": "/m/01c648"},
    {"id": 287, "name": "Boy", "freebase_id": "/m/01bl7v"},
    {"id": 288, "name": "Picture frame", "freebase_id": "/m/06z37_"},
    {"id": 289, "name": "Bird", "freebase_id": "/m/015p6"},
    {"id": 290, "name": "Car", "freebase_id": "/m/0k4j"},
    {"id": 291, "name": "Shorts", "freebase_id": "/m/01bfm9"},
    {"id": 292, "name": "Woman", "freebase_id": "/m/03bt1vf"},
    {"id": 293, "name": "Platter", "freebase_id": "/m/099ssp"},
    {"id": 294, "name": "Tie", "freebase_id": "/m/01rkbr"},
    {"id": 295, "name": "Girl", "freebase_id": "/m/05r655"},
    {"id": 296, "name": "Skyscraper", "freebase_id": "/m/079cl"},
    {"id": 297, "name": "Person", "freebase_id": "/m/01g317"},
    {"id": 298, "name": "Flag", "freebase_id": "/m/03120"},
    {"id": 299, "name": "Jeans", "freebase_id": "/m/0fly7"},
    {"id": 300, "name": "Dress", "freebase_id": "/m/01d40f"},
]


def _get_builtin_metadata(cats, class_image_count=None):
    id_to_name = {x["id"]: x["name"] for x in cats}
    thing_dataset_id_to_contiguous_id = {i + 1: i for i in range(len(cats))}
    thing_classes = [x["name"] for x in sorted(cats, key=lambda x: x["id"])]
    return {
        "thing_dataset_id_to_contiguous_id": thing_dataset_id_to_contiguous_id,
        "thing_classes": thing_classes,
        "class_image_count": class_image_count,
    }


_PREDEFINED_SPLITS_OID = {
    "oid_train": ("oid/images/train/", "oid/annotations/oid_challenge_2019_train_bbox.json"),
    "oid_val": ("oid/images/validation/", "oid/annotations/oid_challenge_2019_val.json"),
    "oid_val_expanded": (
        "oid/images/validation/",
        "oid/annotations/oid_challenge_2019_val_expanded.json",
    ),
    "oid_kaggle_test": ("oid/images/test/", "oid/annotations/oid_kaggle_test_image_info.json"),
}


for key, (image_root, json_file) in _PREDEFINED_SPLITS_OID.items():
    register_oid_instances(
        key,
        _get_builtin_metadata(OPENIMAGES_2019_CATEGORIES),
        os.path.join("datasets", json_file) if "://" not in json_file else json_file,
        os.path.join("datasets", image_root),
    )

_PREDEFINED_SPLITS_OID_SEG = {
    "oid_seg_train": ("oid/images/train/", "oid/annotations/openimages_instances_train.json"),
    "oid_seg_val": ("oid/images/validation/", "oid/annotations/openimages_instances_val.json"),
    "oid_seg_kaggle_test": (
        "oid/images/test/",
        "oid/annotations/openimages_instances_kaggle_test_image_info.json",
    ),
}


for key, (image_root, json_file) in _PREDEFINED_SPLITS_OID_SEG.items():
    register_oid_instances(
        key,
        _get_builtin_metadata(categories_seg),
        os.path.join("datasets", json_file) if "://" not in json_file else json_file,
        os.path.join("datasets", image_root),
    )


_PREDEFINED_SPLITS_OPENIMAGES_DETECTION = {
    "openimages_challenge_2019_train": (
        "openimages/train/",
        "openimages/annotations/openimages_challenge_2019_train_bbox.json",
    ),
    "openimages_challenge_2019_val": (
        "openimages/validation/",
        "openimages/annotations/openimages_challenge_2019_val_bbox.json",
    ),
}


for key, (image_root, json_file) in _PREDEFINED_SPLITS_OPENIMAGES_DETECTION.items():
    register_oid_instances(
        key,
        _get_builtin_metadata(OPENIMAGES_2019_CATEGORIES),
        os.path.join("datasets", json_file) if "://" not in json_file else json_file,
        os.path.join("datasets", image_root),
    )


_PREDEFINED_SPLITS_OPENIMAGES_V6_DETECTION = {
    "openimages_v6_train_bbox": (
        "openimages/train/",
        "openimages/annotations/openimages_v6_train_bbox.json",
    ),
    "openimages_v6_train_bbox_nogroup": (
        "openimages/train/",
        "openimages/annotations/openimages_v6_train_bbox_nogroup.json",
    ),
    "openimages_v6_val_bbox": (
        "openimages/validation/",
        "openimages/annotations/openimages_v6_val_bbox.json",
    ),
    "openimages_v6_val_bbox_nogroup": (
        "openimages/validation/",
        "openimages/annotations/openimages_v6_val_bbox_nogroup.json",
    ),
    "openimages_v6_train_instance": (
        "openimages/train/",
        "openimages/annotations/openimages_v6_train_instance.json",
    ),
    "openimages_v6_val_instance": (
        "openimages/validation/",
        "openimages/annotations/openimages_v6_val_instance.json",
    ),
    "openimages_v6_train_bbox_instance": (
        "openimages/train/",
        "openimages/annotations/openimages_v6_train_bbox_instance.json",
    ),
    "openimages_v6_val_bbox_instance": (
        "openimages/validation/",
        "openimages/annotations/openimages_v6_val_bbox_instance.json",
    ),
}


def register_all_oid(root):
    for key, (image_root, json_file) in _PREDEFINED_SPLITS_OPENIMAGES_V6_DETECTION.items():
        register_oid_instances(
            key,
            _get_builtin_metadata(OPENIMAGES_V6_CATEGORIES, OPENIMAGES_v6_CATEGORY_IMAGE_COUNT),
            os.path.join(root, json_file) if "://" not in json_file else json_file,
            os.path.join(root, image_root),
        )


if __name__.endswith(".oid"):
    # Assume pre-defined datasets live in `./datasets`.
    _root = os.getenv("DETECTRON2_DATASETS", "datasets")
    register_all_oid(_root)
