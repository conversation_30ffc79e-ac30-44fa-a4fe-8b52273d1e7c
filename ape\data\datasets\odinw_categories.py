ODINW_CATEGORIES = {
    "AerialMaritimeDrone": [
        {"id": 1, "name": "boat", "supercategory": "movable-objects"},
        {"id": 2, "name": "car", "supercategory": "movable-objects"},
        {"id": 3, "name": "dock", "supercategory": "movable-objects"},
        {"id": 4, "name": "jetski", "supercategory": "movable-objects"},
        {"id": 5, "name": "lift", "supercategory": "movable-objects"},
    ],
    "AmericanSignLanguageLetters": [
        {"id": 1, "name": "A", "supercategory": "Letters"},
        {"id": 2, "name": "B", "supercategory": "Letters"},
        {"id": 3, "name": "C", "supercategory": "Letters"},
        {"id": 4, "name": "D", "supercategory": "Letters"},
        {"id": 5, "name": "E", "supercategory": "Letters"},
        {"id": 6, "name": "F", "supercategory": "Letters"},
        {"id": 7, "name": "G", "supercategory": "Letters"},
        {"id": 8, "name": "H", "supercategory": "Letters"},
        {"id": 9, "name": "I", "supercategory": "Letters"},
        {"id": 10, "name": "J", "supercategory": "Letters"},
        {"id": 11, "name": "K", "supercategory": "Letters"},
        {"id": 12, "name": "L", "supercategory": "Letters"},
        {"id": 13, "name": "M", "supercategory": "Letters"},
        {"id": 14, "name": "N", "supercategory": "Letters"},
        {"id": 15, "name": "O", "supercategory": "Letters"},
        {"id": 16, "name": "P", "supercategory": "Letters"},
        {"id": 17, "name": "Q", "supercategory": "Letters"},
        {"id": 18, "name": "R", "supercategory": "Letters"},
        {"id": 19, "name": "S", "supercategory": "Letters"},
        {"id": 20, "name": "T", "supercategory": "Letters"},
        {"id": 21, "name": "U", "supercategory": "Letters"},
        {"id": 22, "name": "V", "supercategory": "Letters"},
        {"id": 23, "name": "W", "supercategory": "Letters"},
        {"id": 24, "name": "X", "supercategory": "Letters"},
        {"id": 25, "name": "Y", "supercategory": "Letters"},
        {"id": 26, "name": "Z", "supercategory": "Letters"},
    ],
    "Aquarium": [
        {"id": 1, "name": "fish", "supercategory": "creatures"},
        {"id": 2, "name": "jellyfish", "supercategory": "creatures"},
        {"id": 3, "name": "penguin", "supercategory": "creatures"},
        {"id": 4, "name": "puffin", "supercategory": "creatures"},
        {"id": 5, "name": "shark", "supercategory": "creatures"},
        {"id": 6, "name": "starfish", "supercategory": "creatures"},
        {"id": 7, "name": "stingray", "supercategory": "creatures"},
    ],
    "BCCD": [
        {"id": 1, "name": "Platelets", "supercategory": "cells"},
        {"id": 2, "name": "RBC", "supercategory": "cells"},
        {"id": 3, "name": "WBC", "supercategory": "cells"},
    ],
    "boggleBoards": [
        {"id": 1, "name": "Q", "supercategory": "letters"},
        {"id": 2, "name": "a", "supercategory": "letters"},
        {"id": 3, "name": "an", "supercategory": "letters"},
        {"id": 4, "name": "b", "supercategory": "letters"},
        {"id": 5, "name": "c", "supercategory": "letters"},
        {"id": 6, "name": "d", "supercategory": "letters"},
        {"id": 7, "name": "e", "supercategory": "letters"},
        {"id": 8, "name": "er", "supercategory": "letters"},
        {"id": 9, "name": "f", "supercategory": "letters"},
        {"id": 10, "name": "g", "supercategory": "letters"},
        {"id": 11, "name": "h", "supercategory": "letters"},
        {"id": 12, "name": "he", "supercategory": "letters"},
        {"id": 13, "name": "i", "supercategory": "letters"},
        {"id": 14, "name": "in", "supercategory": "letters"},
        {"id": 15, "name": "j", "supercategory": "letters"},
        {"id": 16, "name": "k", "supercategory": "letters"},
        {"id": 17, "name": "l", "supercategory": "letters"},
        {"id": 18, "name": "m", "supercategory": "letters"},
        {"id": 19, "name": "n", "supercategory": "letters"},
        {"id": 20, "name": "o", "supercategory": "letters"},
        {"id": 21, "name": "o ", "supercategory": "letters"},
        {"id": 22, "name": "p", "supercategory": "letters"},
        {"id": 23, "name": "q", "supercategory": "letters"},
        {"id": 24, "name": "qu", "supercategory": "letters"},
        {"id": 25, "name": "r", "supercategory": "letters"},
        {"id": 26, "name": "s", "supercategory": "letters"},
        {"id": 27, "name": "t", "supercategory": "letters"},
        {"id": 28, "name": "t\\", "supercategory": "letters"},
        {"id": 29, "name": "th", "supercategory": "letters"},
        {"id": 30, "name": "u", "supercategory": "letters"},
        {"id": 31, "name": "v", "supercategory": "letters"},
        {"id": 32, "name": "w", "supercategory": "letters"},
        {"id": 33, "name": "wild", "supercategory": "letters"},
        {"id": 34, "name": "x", "supercategory": "letters"},
        {"id": 35, "name": "y", "supercategory": "letters"},
        {"id": 36, "name": "z", "supercategory": "letters"},
    ],
    "brackishUnderwater": [
        {"id": 1, "name": "crab", "supercategory": "animals"},
        {"id": 2, "name": "fish", "supercategory": "animals"},
        {"id": 3, "name": "jellyfish", "supercategory": "animals"},
        {"id": 4, "name": "shrimp", "supercategory": "animals"},
        {"id": 5, "name": "small_fish", "supercategory": "animals"},
        {"id": 6, "name": "starfish", "supercategory": "animals"},
    ],
    "ChessPieces": [
        {"id": 1, "name": "bishop", "supercategory": "pieces"},
        {"id": 2, "name": "black-bishop", "supercategory": "pieces"},
        {"id": 3, "name": "black-king", "supercategory": "pieces"},
        {"id": 4, "name": "black-knight", "supercategory": "pieces"},
        {"id": 5, "name": "black-pawn", "supercategory": "pieces"},
        {"id": 6, "name": "black-queen", "supercategory": "pieces"},
        {"id": 7, "name": "black-rook", "supercategory": "pieces"},
        {"id": 8, "name": "white-bishop", "supercategory": "pieces"},
        {"id": 9, "name": "white-king", "supercategory": "pieces"},
        {"id": 10, "name": "white-knight", "supercategory": "pieces"},
        {"id": 11, "name": "white-pawn", "supercategory": "pieces"},
        {"id": 12, "name": "white-queen", "supercategory": "pieces"},
        {"id": 13, "name": "white-rook", "supercategory": "pieces"},
    ],
    "CottontailRabbits": [
        {"id": 1, "name": "Cottontail-Rabbit", "supercategory": "Cottontail-Rabbit"}
    ],
    "dice": [
        {"id": 1, "name": "1", "supercategory": "dice"},
        {"id": 2, "name": "2", "supercategory": "dice"},
        {"id": 3, "name": "3", "supercategory": "dice"},
        {"id": 4, "name": "4", "supercategory": "dice"},
        {"id": 5, "name": "5", "supercategory": "dice"},
        {"id": 6, "name": "6", "supercategory": "dice"},
    ],
    "DroneControl": [
        {"id": 1, "name": "follow", "supercategory": "actions"},
        {"id": 2, "name": "follow_hand", "supercategory": "actions"},
        {"id": 3, "name": "land", "supercategory": "actions"},
        {"id": 4, "name": "land_hand", "supercategory": "actions"},
        {"id": 5, "name": "null", "supercategory": "actions"},
        {"id": 6, "name": "object", "supercategory": "actions"},
        {"id": 7, "name": "takeoff", "supercategory": "actions"},
        {"id": 8, "name": "takeoff-hand", "supercategory": "actions"},
    ],
    "EgoHands-generic": [
        {"id": 1, "name": "hand", "supercategory": "hands"},
    ],
    "EgoHands-specific": [
        {"id": 1, "name": "myleft", "supercategory": "hands"},
        {"id": 2, "name": "myright", "supercategory": "hands"},
        {"id": 3, "name": "yourleft", "supercategory": "hands"},
        {"id": 4, "name": "yourright", "supercategory": "hands"},
    ],
    "HardHatWorkers": [
        {"id": 1, "name": "head", "supercategory": "Workers"},
        {"id": 2, "name": "helmet", "supercategory": "Workers"},
        {"id": 3, "name": "person", "supercategory": "Workers"},
    ],
    "MaskWearing": [
        {"id": 1, "name": "mask", "supercategory": "People"},
        {"id": 2, "name": "no-mask", "supercategory": "People"},
    ],
    "MountainDewCommercial": [
        {"id": 1, "name": "bottle", "supercategory": "bottles"},
    ],
    "NorthAmericaMushrooms": [
        {"id": 1, "name": "CoW", "supercategory": "mushroom"},
        {"id": 2, "name": "chanterelle", "supercategory": "mushroom"},
    ],
    "openPoetryVision": [
        {"id": 1, "name": "American Typewriter", "supercategory": "text"},
        {"id": 2, "name": "Andale Mono", "supercategory": "text"},
        {"id": 3, "name": "Apple Chancery", "supercategory": "text"},
        {"id": 4, "name": "Arial", "supercategory": "text"},
        {"id": 5, "name": "Avenir", "supercategory": "text"},
        {"id": 6, "name": "Baskerville", "supercategory": "text"},
        {"id": 7, "name": "Big Caslon", "supercategory": "text"},
        {"id": 8, "name": "Bradley Hand", "supercategory": "text"},
        {"id": 9, "name": "Brush Script MT", "supercategory": "text"},
        {"id": 10, "name": "Chalkboard", "supercategory": "text"},
        {"id": 11, "name": "Comic Sans MS", "supercategory": "text"},
        {"id": 12, "name": "Copperplate", "supercategory": "text"},
        {"id": 13, "name": "Courier", "supercategory": "text"},
        {"id": 14, "name": "Didot", "supercategory": "text"},
        {"id": 15, "name": "Futura", "supercategory": "text"},
        {"id": 16, "name": "Geneva", "supercategory": "text"},
        {"id": 17, "name": "Georgia", "supercategory": "text"},
        {"id": 18, "name": "Gill Sans", "supercategory": "text"},
        {"id": 19, "name": "Helvetica", "supercategory": "text"},
        {"id": 20, "name": "Herculanum", "supercategory": "text"},
        {"id": 21, "name": "Impact", "supercategory": "text"},
        {"id": 22, "name": "Kefa", "supercategory": "text"},
        {"id": 23, "name": "Lucida Grande", "supercategory": "text"},
        {"id": 24, "name": "Luminari", "supercategory": "text"},
        {"id": 25, "name": "Marker Felt", "supercategory": "text"},
        {"id": 26, "name": "Menlo", "supercategory": "text"},
        {"id": 27, "name": "Monaco", "supercategory": "text"},
        {"id": 28, "name": "Noteworthy", "supercategory": "text"},
        {"id": 29, "name": "Optima", "supercategory": "text"},
        {"id": 30, "name": "PT Sans", "supercategory": "text"},
        {"id": 31, "name": "PT Serif", "supercategory": "text"},
        {"id": 32, "name": "Palatino", "supercategory": "text"},
        {"id": 33, "name": "Papyrus", "supercategory": "text"},
        {"id": 34, "name": "Phosphate", "supercategory": "text"},
        {"id": 35, "name": "Rockwell", "supercategory": "text"},
        {"id": 36, "name": "SF Pro", "supercategory": "text"},
        {"id": 37, "name": "SignPainter", "supercategory": "text"},
        {"id": 38, "name": "Skia", "supercategory": "text"},
        {"id": 39, "name": "Snell Roundhand", "supercategory": "text"},
        {"id": 40, "name": "Tahoma", "supercategory": "text"},
        {"id": 41, "name": "Times New Roman", "supercategory": "text"},
        {"id": 42, "name": "Trebuchet MS", "supercategory": "text"},
        {"id": 43, "name": "Verdana", "supercategory": "text"},
    ],
    "OxfordPets-by-breed": [
        {"id": 1, "name": "cat-Abyssinian", "supercategory": "pets"},
        {"id": 2, "name": "cat-Bengal", "supercategory": "pets"},
        {"id": 3, "name": "cat-Birman", "supercategory": "pets"},
        {"id": 4, "name": "cat-Bombay", "supercategory": "pets"},
        {"id": 5, "name": "cat-British_Shorthair", "supercategory": "pets"},
        {"id": 6, "name": "cat-Egyptian_Mau", "supercategory": "pets"},
        {"id": 7, "name": "cat-Maine_Coon", "supercategory": "pets"},
        {"id": 8, "name": "cat-Persian", "supercategory": "pets"},
        {"id": 9, "name": "cat-Ragdoll", "supercategory": "pets"},
        {"id": 10, "name": "cat-Russian_Blue", "supercategory": "pets"},
        {"id": 11, "name": "cat-Siamese", "supercategory": "pets"},
        {"id": 12, "name": "cat-Sphynx", "supercategory": "pets"},
        {"id": 13, "name": "dog-american_bulldog", "supercategory": "pets"},
        {"id": 14, "name": "dog-american_pit_bull_terrier", "supercategory": "pets"},
        {"id": 15, "name": "dog-basset_hound", "supercategory": "pets"},
        {"id": 16, "name": "dog-beagle", "supercategory": "pets"},
        {"id": 17, "name": "dog-boxer", "supercategory": "pets"},
        {"id": 18, "name": "dog-chihuahua", "supercategory": "pets"},
        {"id": 19, "name": "dog-english_cocker_spaniel", "supercategory": "pets"},
        {"id": 20, "name": "dog-english_setter", "supercategory": "pets"},
        {"id": 21, "name": "dog-german_shorthaired", "supercategory": "pets"},
        {"id": 22, "name": "dog-great_pyrenees", "supercategory": "pets"},
        {"id": 23, "name": "dog-havanese", "supercategory": "pets"},
        {"id": 24, "name": "dog-japanese_chin", "supercategory": "pets"},
        {"id": 25, "name": "dog-keeshond", "supercategory": "pets"},
        {"id": 26, "name": "dog-leonberger", "supercategory": "pets"},
        {"id": 27, "name": "dog-miniature_pinscher", "supercategory": "pets"},
        {"id": 28, "name": "dog-newfoundland", "supercategory": "pets"},
        {"id": 29, "name": "dog-pomeranian", "supercategory": "pets"},
        {"id": 30, "name": "dog-pug", "supercategory": "pets"},
        {"id": 31, "name": "dog-saint_bernard", "supercategory": "pets"},
        {"id": 32, "name": "dog-samoyed", "supercategory": "pets"},
        {"id": 33, "name": "dog-scottish_terrier", "supercategory": "pets"},
        {"id": 34, "name": "dog-shiba_inu", "supercategory": "pets"},
        {"id": 35, "name": "dog-staffordshire_bull_terrier", "supercategory": "pets"},
        {"id": 36, "name": "dog-wheaten_terrier", "supercategory": "pets"},
        {"id": 37, "name": "dog-yorkshire_terrier", "supercategory": "pets"},
    ],
    "OxfordPets-by-species": [
        {"id": 1, "name": "cat", "supercategory": "pets"},
        {"id": 2, "name": "dog", "supercategory": "pets"},
    ],
    "Packages": [{"id": 1, "name": "package", "supercategory": "packages"}],
    "PascalVOC": [
        {"id": 1, "name": "aeroplane", "supercategory": "VOC"},
        {"id": 2, "name": "bicycle", "supercategory": "VOC"},
        {"id": 3, "name": "bird", "supercategory": "VOC"},
        {"id": 4, "name": "boat", "supercategory": "VOC"},
        {"id": 5, "name": "bottle", "supercategory": "VOC"},
        {"id": 6, "name": "bus", "supercategory": "VOC"},
        {"id": 7, "name": "car", "supercategory": "VOC"},
        {"id": 8, "name": "cat", "supercategory": "VOC"},
        {"id": 9, "name": "chair", "supercategory": "VOC"},
        {"id": 10, "name": "cow", "supercategory": "VOC"},
        {"id": 11, "name": "diningtable", "supercategory": "VOC"},
        {"id": 12, "name": "dog", "supercategory": "VOC"},
        {"id": 13, "name": "horse", "supercategory": "VOC"},
        {"id": 14, "name": "motorbike", "supercategory": "VOC"},
        {"id": 15, "name": "person", "supercategory": "VOC"},
        {"id": 16, "name": "pottedplant", "supercategory": "VOC"},
        {"id": 17, "name": "sheep", "supercategory": "VOC"},
        {"id": 18, "name": "sofa", "supercategory": "VOC"},
        {"id": 19, "name": "train", "supercategory": "VOC"},
        {"id": 20, "name": "tvmonitor", "supercategory": "VOC"},
    ],
    "pistols": [
        {"id": 1, "name": "pistol", "supercategory": "Guns"},
    ],
    "PKLot": [
        {"id": 1, "name": "space-empty", "supercategory": "spaces"},
        {"id": 2, "name": "space-occupied", "supercategory": "spaces"},
    ],
    "plantdoc": [
        {"id": 1, "name": "Apple Scab Leaf", "supercategory": "leaves"},
        {"id": 2, "name": "Apple leaf", "supercategory": "leaves"},
        {"id": 3, "name": "Apple rust leaf", "supercategory": "leaves"},
        {"id": 4, "name": "Bell_pepper leaf", "supercategory": "leaves"},
        {"id": 5, "name": "Bell_pepper leaf spot", "supercategory": "leaves"},
        {"id": 6, "name": "Blueberry leaf", "supercategory": "leaves"},
        {"id": 7, "name": "Cherry leaf", "supercategory": "leaves"},
        {"id": 8, "name": "Corn Gray leaf spot", "supercategory": "leaves"},
        {"id": 9, "name": "Corn leaf blight", "supercategory": "leaves"},
        {"id": 10, "name": "Corn rust leaf", "supercategory": "leaves"},
        {"id": 11, "name": "Peach leaf", "supercategory": "leaves"},
        {"id": 12, "name": "Potato leaf", "supercategory": "leaves"},
        {"id": 13, "name": "Potato leaf early blight", "supercategory": "leaves"},
        {"id": 14, "name": "Potato leaf late blight", "supercategory": "leaves"},
        {"id": 15, "name": "Raspberry leaf", "supercategory": "leaves"},
        {"id": 16, "name": "Soyabean leaf", "supercategory": "leaves"},
        {"id": 17, "name": "Soybean leaf", "supercategory": "leaves"},
        {"id": 18, "name": "Squash Powdery mildew leaf", "supercategory": "leaves"},
        {"id": 19, "name": "Strawberry leaf", "supercategory": "leaves"},
        {"id": 20, "name": "Tomato Early blight leaf", "supercategory": "leaves"},
        {"id": 21, "name": "Tomato Septoria leaf spot", "supercategory": "leaves"},
        {"id": 22, "name": "Tomato leaf", "supercategory": "leaves"},
        {"id": 23, "name": "Tomato leaf bacterial spot", "supercategory": "leaves"},
        {"id": 24, "name": "Tomato leaf late blight", "supercategory": "leaves"},
        {"id": 25, "name": "Tomato leaf mosaic virus", "supercategory": "leaves"},
        {"id": 26, "name": "Tomato leaf yellow virus", "supercategory": "leaves"},
        {"id": 27, "name": "Tomato mold leaf", "supercategory": "leaves"},
        {"id": 28, "name": "Tomato two spotted spider mites leaf", "supercategory": "leaves"},
        {"id": 29, "name": "grape leaf", "supercategory": "leaves"},
        {"id": 30, "name": "grape leaf black rot", "supercategory": "leaves"},
    ],
    "pothole": [
        {"id": 1, "name": "pothole", "supercategory": "potholes"},
    ],
    "Raccoon": [
        {"id": 1, "name": "raccoon", "supercategory": "raccoons"},
    ],
    "selfdrivingCar": [
        {"id": 1, "name": "biker", "supercategory": "obstacles"},
        {"id": 2, "name": "car", "supercategory": "obstacles"},
        {"id": 3, "name": "pedestrian", "supercategory": "obstacles"},
        {"id": 4, "name": "trafficLight", "supercategory": "obstacles"},
        {"id": 5, "name": "trafficLight-Green", "supercategory": "obstacles"},
        {"id": 6, "name": "trafficLight-GreenLeft", "supercategory": "obstacles"},
        {"id": 7, "name": "trafficLight-Red", "supercategory": "obstacles"},
        {"id": 8, "name": "trafficLight-RedLeft", "supercategory": "obstacles"},
        {"id": 9, "name": "trafficLight-Yellow", "supercategory": "obstacles"},
        {"id": 10, "name": "trafficLight-YellowLeft", "supercategory": "obstacles"},
        {"id": 11, "name": "truck", "supercategory": "obstacles"},
    ],
    "ShellfishOpenImages": [
        {"id": 1, "name": "Crab", "supercategory": "shellfish"},
        {"id": 2, "name": "Lobster", "supercategory": "shellfish"},
        {"id": 3, "name": "Shrimp", "supercategory": "shellfish"},
    ],
    "ThermalCheetah": [
        {"id": 1, "name": "cheetah", "supercategory": "cheetah"},
        {"id": 2, "name": "human", "supercategory": "cheetah"},
    ],
    "thermalDogsAndPeople": [
        {"id": 1, "name": "dog", "supercategory": "dogs-person"},
        {"id": 2, "name": "person", "supercategory": "dogs-person"},
    ],
    "UnoCards": [
        {"id": 1, "name": "0", "supercategory": "Card-Types"},
        {"id": 2, "name": "1", "supercategory": "Card-Types"},
        {"id": 3, "name": "2", "supercategory": "Card-Types"},
        {"id": 4, "name": "3", "supercategory": "Card-Types"},
        {"id": 5, "name": "4", "supercategory": "Card-Types"},
        {"id": 6, "name": "5", "supercategory": "Card-Types"},
        {"id": 7, "name": "6", "supercategory": "Card-Types"},
        {"id": 8, "name": "7", "supercategory": "Card-Types"},
        {"id": 9, "name": "8", "supercategory": "Card-Types"},
        {"id": 10, "name": "9", "supercategory": "Card-Types"},
        {"id": 11, "name": "10", "supercategory": "Card-Types"},
        {"id": 12, "name": "11", "supercategory": "Card-Types"},
        {"id": 13, "name": "12", "supercategory": "Card-Types"},
        {"id": 14, "name": "13", "supercategory": "Card-Types"},
        {"id": 15, "name": "14", "supercategory": "Card-Types"},
    ],
    "VehiclesOpenImages": [
        {"id": 1, "name": "Ambulance", "supercategory": "vehicles"},
        {"id": 2, "name": "Bus", "supercategory": "vehicles"},
        {"id": 3, "name": "Car", "supercategory": "vehicles"},
        {"id": 4, "name": "Motorcycle", "supercategory": "vehicles"},
        {"id": 5, "name": "Truck", "supercategory": "vehicles"},
    ],
    "websiteScreenshots": [
        {"id": 1, "name": "button", "supercategory": "elements"},
        {"id": 2, "name": "field", "supercategory": "elements"},
        {"id": 3, "name": "heading", "supercategory": "elements"},
        {"id": 4, "name": "iframe", "supercategory": "elements"},
        {"id": 5, "name": "image", "supercategory": "elements"},
        {"id": 6, "name": "label", "supercategory": "elements"},
        {"id": 7, "name": "link", "supercategory": "elements"},
        {"id": 8, "name": "text", "supercategory": "elements"},
    ],
    "WildfireSmoke": [
        {"id": 1, "name": "smoke", "supercategory": "Smoke"},
    ],
}
