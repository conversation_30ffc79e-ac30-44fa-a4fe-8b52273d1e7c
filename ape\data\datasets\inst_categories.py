categories = {
    "coco": [
        {"color": [220, 20, 60], "isthing": 1, "id": 1, "name": "person"},
        {"color": [119, 11, 32], "isthing": 1, "id": 2, "name": "bicycle"},
        {"color": [0, 0, 142], "isthing": 1, "id": 3, "name": "car"},
        {"color": [0, 0, 230], "isthing": 1, "id": 4, "name": "motorcycle"},
        {"color": [106, 0, 228], "isthing": 1, "id": 5, "name": "airplane"},
        {"color": [0, 60, 100], "isthing": 1, "id": 6, "name": "bus"},
        {"color": [0, 80, 100], "isthing": 1, "id": 7, "name": "train"},
        {"color": [0, 0, 70], "isthing": 1, "id": 8, "name": "truck"},
        {"color": [0, 0, 192], "isthing": 1, "id": 9, "name": "boat"},
        {"color": [250, 170, 30], "isthing": 1, "id": 10, "name": "traffic light"},
        {"color": [100, 170, 30], "isthing": 1, "id": 11, "name": "fire hydrant"},
        {"color": [220, 220, 0], "isthing": 1, "id": 13, "name": "stop sign"},
        {"color": [175, 116, 175], "isthing": 1, "id": 14, "name": "parking meter"},
        {"color": [250, 0, 30], "isthing": 1, "id": 15, "name": "bench"},
        {"color": [165, 42, 42], "isthing": 1, "id": 16, "name": "bird"},
        {"color": [255, 77, 255], "isthing": 1, "id": 17, "name": "cat"},
        {"color": [0, 226, 252], "isthing": 1, "id": 18, "name": "dog"},
        {"color": [182, 182, 255], "isthing": 1, "id": 19, "name": "horse"},
        {"color": [0, 82, 0], "isthing": 1, "id": 20, "name": "sheep"},
        {"color": [120, 166, 157], "isthing": 1, "id": 21, "name": "cow"},
        {"color": [110, 76, 0], "isthing": 1, "id": 22, "name": "elephant"},
        {"color": [174, 57, 255], "isthing": 1, "id": 23, "name": "bear"},
        {"color": [199, 100, 0], "isthing": 1, "id": 24, "name": "zebra"},
        {"color": [72, 0, 118], "isthing": 1, "id": 25, "name": "giraffe"},
        {"color": [255, 179, 240], "isthing": 1, "id": 27, "name": "backpack"},
        {"color": [0, 125, 92], "isthing": 1, "id": 28, "name": "umbrella"},
        {"color": [209, 0, 151], "isthing": 1, "id": 31, "name": "handbag"},
        {"color": [188, 208, 182], "isthing": 1, "id": 32, "name": "tie"},
        {"color": [0, 220, 176], "isthing": 1, "id": 33, "name": "suitcase"},
        {"color": [255, 99, 164], "isthing": 1, "id": 34, "name": "frisbee"},
        {"color": [92, 0, 73], "isthing": 1, "id": 35, "name": "skis"},
        {"color": [133, 129, 255], "isthing": 1, "id": 36, "name": "snowboard"},
        {"color": [78, 180, 255], "isthing": 1, "id": 37, "name": "sports ball"},
        {"color": [0, 228, 0], "isthing": 1, "id": 38, "name": "kite"},
        {"color": [174, 255, 243], "isthing": 1, "id": 39, "name": "baseball bat"},
        {"color": [45, 89, 255], "isthing": 1, "id": 40, "name": "baseball glove"},
        {"color": [134, 134, 103], "isthing": 1, "id": 41, "name": "skateboard"},
        {"color": [145, 148, 174], "isthing": 1, "id": 42, "name": "surfboard"},
        {"color": [255, 208, 186], "isthing": 1, "id": 43, "name": "tennis racket"},
        {"color": [197, 226, 255], "isthing": 1, "id": 44, "name": "bottle"},
        {"color": [171, 134, 1], "isthing": 1, "id": 46, "name": "wine glass"},
        {"color": [109, 63, 54], "isthing": 1, "id": 47, "name": "cup"},
        {"color": [207, 138, 255], "isthing": 1, "id": 48, "name": "fork"},
        {"color": [151, 0, 95], "isthing": 1, "id": 49, "name": "knife"},
        {"color": [9, 80, 61], "isthing": 1, "id": 50, "name": "spoon"},
        {"color": [84, 105, 51], "isthing": 1, "id": 51, "name": "bowl"},
        {"color": [74, 65, 105], "isthing": 1, "id": 52, "name": "banana"},
        {"color": [166, 196, 102], "isthing": 1, "id": 53, "name": "apple"},
        {"color": [208, 195, 210], "isthing": 1, "id": 54, "name": "sandwich"},
        {"color": [255, 109, 65], "isthing": 1, "id": 55, "name": "orange"},
        {"color": [0, 143, 149], "isthing": 1, "id": 56, "name": "broccoli"},
        {"color": [179, 0, 194], "isthing": 1, "id": 57, "name": "carrot"},
        {"color": [209, 99, 106], "isthing": 1, "id": 58, "name": "hot dog"},
        {"color": [5, 121, 0], "isthing": 1, "id": 59, "name": "pizza"},
        {"color": [227, 255, 205], "isthing": 1, "id": 60, "name": "donut"},
        {"color": [147, 186, 208], "isthing": 1, "id": 61, "name": "cake"},
        {"color": [153, 69, 1], "isthing": 1, "id": 62, "name": "chair"},
        {"color": [3, 95, 161], "isthing": 1, "id": 63, "name": "couch"},
        {"color": [163, 255, 0], "isthing": 1, "id": 64, "name": "potted plant"},
        {"color": [119, 0, 170], "isthing": 1, "id": 65, "name": "bed"},
        {"color": [0, 182, 199], "isthing": 1, "id": 67, "name": "dining table"},
        {"color": [0, 165, 120], "isthing": 1, "id": 70, "name": "toilet"},
        {"color": [183, 130, 88], "isthing": 1, "id": 72, "name": "tv"},
        {"color": [95, 32, 0], "isthing": 1, "id": 73, "name": "laptop"},
        {"color": [130, 114, 135], "isthing": 1, "id": 74, "name": "mouse"},
        {"color": [110, 129, 133], "isthing": 1, "id": 75, "name": "remote"},
        {"color": [166, 74, 118], "isthing": 1, "id": 76, "name": "keyboard"},
        {"color": [219, 142, 185], "isthing": 1, "id": 77, "name": "cell phone"},
        {"color": [79, 210, 114], "isthing": 1, "id": 78, "name": "microwave"},
        {"color": [178, 90, 62], "isthing": 1, "id": 79, "name": "oven"},
        {"color": [65, 70, 15], "isthing": 1, "id": 80, "name": "toaster"},
        {"color": [127, 167, 115], "isthing": 1, "id": 81, "name": "sink"},
        {"color": [59, 105, 106], "isthing": 1, "id": 82, "name": "refrigerator"},
        {"color": [142, 108, 45], "isthing": 1, "id": 84, "name": "book"},
        {"color": [196, 172, 0], "isthing": 1, "id": 85, "name": "clock"},
        {"color": [95, 54, 80], "isthing": 1, "id": 86, "name": "vase"},
        {"color": [128, 76, 255], "isthing": 1, "id": 87, "name": "scissors"},
        {"color": [201, 57, 1], "isthing": 1, "id": 88, "name": "teddy bear"},
        {"color": [246, 0, 122], "isthing": 1, "id": 89, "name": "hair drier"},
        {"color": [191, 162, 208], "isthing": 1, "id": 90, "name": "toothbrush"},
    ],
    "cityscapes": [
        {"id": i + 1, "name": x}
        for i, x in enumerate(
            ["person", "rider", "car", "truck", "bus", "train", "motorcycle", "bicycle"]
        )
    ],
    "mapillary": [
        {"id": 1, "name": "animal--bird"},
        {"id": 2, "name": "animal--ground-animal"},
        {"id": 9, "name": "construction--flat--crosswalk-plain"},
        {"id": 20, "name": "human--person"},
        {"id": 21, "name": "human--rider--bicyclist"},
        {"id": 22, "name": "human--rider--motorcyclist"},
        {"id": 23, "name": "human--rider--other-rider"},
        {"id": 24, "name": "marking--crosswalk-zebra"},
        {"id": 33, "name": "object--banner"},
        {"id": 34, "name": "object--bench"},
        {"id": 35, "name": "object--bike-rack"},
        {"id": 36, "name": "object--billboard"},
        {"id": 37, "name": "object--catch-basin"},
        {"id": 38, "name": "object--cctv-camera"},
        {"id": 39, "name": "object--fire-hydrant"},
        {"id": 40, "name": "object--junction-box"},
        {"id": 41, "name": "object--mailbox"},
        {"id": 42, "name": "object--manhole"},
        {"id": 43, "name": "object--phone-booth"},
        {"id": 45, "name": "object--street-light"},
        {"id": 46, "name": "object--support--pole"},
        {"id": 47, "name": "object--support--traffic-sign-frame"},
        {"id": 48, "name": "object--support--utility-pole"},
        {"id": 49, "name": "object--traffic-light"},
        {"id": 50, "name": "object--traffic-sign--back"},
        {"id": 51, "name": "object--traffic-sign--front"},
        {"id": 52, "name": "object--trash-can"},
        {"id": 53, "name": "object--vehicle--bicycle"},
        {"id": 54, "name": "object--vehicle--boat"},
        {"id": 55, "name": "object--vehicle--bus"},
        {"id": 56, "name": "object--vehicle--car"},
        {"id": 57, "name": "object--vehicle--caravan"},
        {"id": 58, "name": "object--vehicle--motorcycle"},
        {"id": 60, "name": "object--vehicle--other-vehicle"},
        {"id": 61, "name": "object--vehicle--trailer"},
        {"id": 62, "name": "object--vehicle--truck"},
        {"id": 63, "name": "object--vehicle--wheeled-slow"},
    ],
    "viper": [
        {"id": 13, "name": "trafficlight", "supercategory": ""},
        {"id": 16, "name": "firehydrant", "supercategory": ""},
        {"id": 17, "name": "chair", "supercategory": ""},
        {"id": 19, "name": "trashcan", "supercategory": ""},
        {"id": 20, "name": "person", "supercategory": ""},
        {"id": 23, "name": "motorcycle", "supercategory": ""},
        {"id": 24, "name": "car", "supercategory": ""},
        {"id": 25, "name": "van", "supercategory": ""},
        {"id": 26, "name": "bus", "supercategory": ""},
        {"id": 27, "name": "truck", "supercategory": ""},
    ],
    "scannet": [
        {"id": 3, "name": "cabinet", "supercategory": "furniture"},
        {"id": 4, "name": "bed", "supercategory": "furniture"},
        {"id": 5, "name": "chair", "supercategory": "furniture"},
        {"id": 6, "name": "sofa", "supercategory": "furniture"},
        {"id": 7, "name": "table", "supercategory": "furniture"},
        {"id": 8, "name": "door", "supercategory": "furniture"},
        {"id": 9, "name": "window", "supercategory": "furniture"},
        {"id": 10, "name": "bookshelf", "supercategory": "furniture"},
        {"id": 11, "name": "picture", "supercategory": "furniture"},
        {"id": 12, "name": "counter", "supercategory": "furniture"},
        {"id": 14, "name": "desk", "supercategory": "furniture"},
        {"id": 16, "name": "curtain", "supercategory": "furniture"},
        {"id": 24, "name": "refrigerator", "supercategory": "appliance"},
        {"id": 28, "name": "shower curtain", "supercategory": "furniture"},
        {"id": 33, "name": "toilet", "supercategory": "furniture"},
        {"id": 34, "name": "sink", "supercategory": "appliance"},
        {"id": 36, "name": "bathtub", "supercategory": "furniture"},
        {"id": 39, "name": "otherfurniture", "supercategory": "furniture"},
    ],
    "oid": [
        {"id": 1, "name": "Screwdriver", "freebase_id": "/m/01bms0"},
        {"id": 2, "name": "Light switch", "freebase_id": "/m/03jbxj"},
        {"id": 3, "name": "Doughnut", "freebase_id": "/m/0jy4k"},
        {"id": 4, "name": "Toilet paper", "freebase_id": "/m/09gtd"},
        {"id": 5, "name": "Wrench", "freebase_id": "/m/01j5ks"},
        {"id": 6, "name": "Toaster", "freebase_id": "/m/01k6s3"},
        {"id": 7, "name": "Tennis ball", "freebase_id": "/m/05ctyq"},
        {"id": 8, "name": "Radish", "freebase_id": "/m/015x5n"},
        {"id": 9, "name": "Pomegranate", "freebase_id": "/m/0jwn_"},
        {"id": 10, "name": "Kite", "freebase_id": "/m/02zt3"},
        {"id": 11, "name": "Table tennis racket", "freebase_id": "/m/05_5p_0"},
        {"id": 12, "name": "Hamster", "freebase_id": "/m/03qrc"},
        {"id": 13, "name": "Barge", "freebase_id": "/m/01btn"},
        {"id": 14, "name": "Shower", "freebase_id": "/m/02f9f_"},
        {"id": 15, "name": "Printer", "freebase_id": "/m/01m4t"},
        {"id": 16, "name": "Snowmobile", "freebase_id": "/m/01x3jk"},
        {"id": 17, "name": "Fire hydrant", "freebase_id": "/m/01pns0"},
        {"id": 18, "name": "Limousine", "freebase_id": "/m/01lcw4"},
        {"id": 19, "name": "Whale", "freebase_id": "/m/084zz"},
        {"id": 20, "name": "Microwave oven", "freebase_id": "/m/0fx9l"},
        {"id": 21, "name": "Asparagus", "freebase_id": "/m/0cjs7"},
        {"id": 22, "name": "Lion", "freebase_id": "/m/096mb"},
        {"id": 23, "name": "Spatula", "freebase_id": "/m/02d1br"},
        {"id": 24, "name": "Torch", "freebase_id": "/m/07dd4"},
        {"id": 25, "name": "Volleyball", "freebase_id": "/m/02rgn06"},
        {"id": 26, "name": "Ambulance", "freebase_id": "/m/012n7d"},
        {"id": 27, "name": "Chopsticks", "freebase_id": "/m/01_5g"},
        {"id": 28, "name": "Raccoon", "freebase_id": "/m/0dq75"},
        {"id": 29, "name": "Blue jay", "freebase_id": "/m/01f8m5"},
        {"id": 30, "name": "Lynx", "freebase_id": "/m/04g2r"},
        {"id": 31, "name": "Dice", "freebase_id": "/m/029b3"},
        {"id": 32, "name": "Filing cabinet", "freebase_id": "/m/047j0r"},
        {"id": 33, "name": "Ruler", "freebase_id": "/m/0hdln"},
        {"id": 34, "name": "Power plugs and sockets", "freebase_id": "/m/03bbps"},
        {"id": 35, "name": "Bell pepper", "freebase_id": "/m/0jg57"},
        {"id": 36, "name": "Binoculars", "freebase_id": "/m/0lt4_"},
        {"id": 37, "name": "Pretzel", "freebase_id": "/m/01f91_"},
        {"id": 38, "name": "Hot dog", "freebase_id": "/m/01b9xk"},
        {"id": 39, "name": "Missile", "freebase_id": "/m/04ylt"},
        {"id": 40, "name": "Common fig", "freebase_id": "/m/043nyj"},
        {"id": 41, "name": "Croissant", "freebase_id": "/m/015wgc"},
        {"id": 42, "name": "Adhesive tape", "freebase_id": "/m/03m3vtv"},
        {"id": 43, "name": "Slow cooker", "freebase_id": "/m/02tsc9"},
        {"id": 44, "name": "Dog bed", "freebase_id": "/m/0h8n6f9"},
        {"id": 45, "name": "Harpsichord", "freebase_id": "/m/03q5t"},
        {"id": 46, "name": "Billiard table", "freebase_id": "/m/04p0qw"},
        {"id": 47, "name": "Alpaca", "freebase_id": "/m/0pcr"},
        {"id": 48, "name": "Harbor seal", "freebase_id": "/m/02l8p9"},
        {"id": 49, "name": "Grape", "freebase_id": "/m/0388q"},
        {"id": 50, "name": "Nail", "freebase_id": "/m/05bm6"},
        {"id": 51, "name": "Paper towel", "freebase_id": "/m/02w3r3"},
        {"id": 52, "name": "Alarm clock", "freebase_id": "/m/046dlr"},
        {"id": 53, "name": "Guacamole", "freebase_id": "/m/02g30s"},
        {"id": 54, "name": "Starfish", "freebase_id": "/m/01h8tj"},
        {"id": 55, "name": "Zebra", "freebase_id": "/m/0898b"},
        {"id": 56, "name": "Segway", "freebase_id": "/m/076bq"},
        {"id": 57, "name": "Sea turtle", "freebase_id": "/m/0120dh"},
        {"id": 58, "name": "Scissors", "freebase_id": "/m/01lsmm"},
        {"id": 59, "name": "Rhinoceros", "freebase_id": "/m/03d443"},
        {"id": 60, "name": "Kangaroo", "freebase_id": "/m/04c0y"},
        {"id": 61, "name": "Jaguar", "freebase_id": "/m/0449p"},
        {"id": 62, "name": "Leopard", "freebase_id": "/m/0c29q"},
        {"id": 63, "name": "Dumbbell", "freebase_id": "/m/04h8sr"},
        {"id": 64, "name": "Envelope", "freebase_id": "/m/0frqm"},
        {"id": 65, "name": "Winter melon", "freebase_id": "/m/02cvgx"},
        {"id": 66, "name": "Teapot", "freebase_id": "/m/01fh4r"},
        {"id": 67, "name": "Camel", "freebase_id": "/m/01x_v"},
        {"id": 68, "name": "Beaker", "freebase_id": "/m/0d20w4"},
        {"id": 69, "name": "Brown bear", "freebase_id": "/m/01dxs"},
        {"id": 70, "name": "Toilet", "freebase_id": "/m/09g1w"},
        {"id": 71, "name": "Teddy bear", "freebase_id": "/m/0kmg4"},
        {"id": 72, "name": "Briefcase", "freebase_id": "/m/0584n8"},
        {"id": 73, "name": "Stop sign", "freebase_id": "/m/02pv19"},
        {"id": 74, "name": "Tiger", "freebase_id": "/m/07dm6"},
        {"id": 75, "name": "Cabbage", "freebase_id": "/m/0fbw6"},
        {"id": 76, "name": "Giraffe", "freebase_id": "/m/03bk1"},
        {"id": 77, "name": "Polar bear", "freebase_id": "/m/0633h"},
        {"id": 78, "name": "Shark", "freebase_id": "/m/0by6g"},
        {"id": 79, "name": "Rabbit", "freebase_id": "/m/06mf6"},
        {"id": 80, "name": "Swim cap", "freebase_id": "/m/04tn4x"},
        {"id": 81, "name": "Pressure cooker", "freebase_id": "/m/0h8ntjv"},
        {"id": 82, "name": "Kitchen knife", "freebase_id": "/m/058qzx"},
        {"id": 83, "name": "Submarine sandwich", "freebase_id": "/m/06pcq"},
        {"id": 84, "name": "Flashlight", "freebase_id": "/m/01kb5b"},
        {"id": 85, "name": "Penguin", "freebase_id": "/m/05z6w"},
        {"id": 86, "name": "Snake", "freebase_id": "/m/078jl"},
        {"id": 87, "name": "Zucchini", "freebase_id": "/m/027pcv"},
        {"id": 88, "name": "Bat", "freebase_id": "/m/01h44"},
        {"id": 89, "name": "Food processor", "freebase_id": "/m/03y6mg"},
        {"id": 90, "name": "Ostrich", "freebase_id": "/m/05n4y"},
        {"id": 91, "name": "Sea lion", "freebase_id": "/m/0gd36"},
        {"id": 92, "name": "Goldfish", "freebase_id": "/m/03fj2"},
        {"id": 93, "name": "Elephant", "freebase_id": "/m/0bwd_0j"},
        {"id": 94, "name": "Rocket", "freebase_id": "/m/09rvcxw"},
        {"id": 95, "name": "Mouse", "freebase_id": "/m/04rmv"},
        {"id": 96, "name": "Oyster", "freebase_id": "/m/0_cp5"},
        {"id": 97, "name": "Digital clock", "freebase_id": "/m/06_72j"},
        {"id": 98, "name": "Otter", "freebase_id": "/m/0cn6p"},
        {"id": 99, "name": "Dolphin", "freebase_id": "/m/02hj4"},
        {"id": 100, "name": "Punching bag", "freebase_id": "/m/0420v5"},
        {"id": 101, "name": "Corded phone", "freebase_id": "/m/0h8lkj8"},
        {"id": 102, "name": "Tennis racket", "freebase_id": "/m/0h8my_4"},
        {"id": 103, "name": "Pancake", "freebase_id": "/m/01dwwc"},
        {"id": 104, "name": "Mango", "freebase_id": "/m/0fldg"},
        {"id": 105, "name": "Crocodile", "freebase_id": "/m/09f_2"},
        {"id": 106, "name": "Waffle", "freebase_id": "/m/01dwsz"},
        {"id": 107, "name": "Computer mouse", "freebase_id": "/m/020lf"},
        {"id": 108, "name": "Kettle", "freebase_id": "/m/03s_tn"},
        {"id": 109, "name": "Tart", "freebase_id": "/m/02zvsm"},
        {"id": 110, "name": "Oven", "freebase_id": "/m/029bxz"},
        {"id": 111, "name": "Banana", "freebase_id": "/m/09qck"},
        {"id": 112, "name": "Cheetah", "freebase_id": "/m/0cd4d"},
        {"id": 113, "name": "Raven", "freebase_id": "/m/06j2d"},
        {"id": 114, "name": "Frying pan", "freebase_id": "/m/04v6l4"},
        {"id": 115, "name": "Pear", "freebase_id": "/m/061_f"},
        {"id": 116, "name": "Fox", "freebase_id": "/m/0306r"},
        {"id": 117, "name": "Skateboard", "freebase_id": "/m/06_fw"},
        {"id": 118, "name": "Rugby ball", "freebase_id": "/m/0wdt60w"},
        {"id": 119, "name": "Watermelon", "freebase_id": "/m/0kpqd"},
        {"id": 120, "name": "Flute", "freebase_id": "/m/0l14j_"},
        {"id": 121, "name": "Canary", "freebase_id": "/m/0ccs93"},
        {"id": 122, "name": "Door handle", "freebase_id": "/m/03c7gz"},
        {"id": 123, "name": "Saxophone", "freebase_id": "/m/06ncr"},
        {"id": 124, "name": "Burrito", "freebase_id": "/m/01j3zr"},
        {"id": 125, "name": "Suitcase", "freebase_id": "/m/01s55n"},
        {"id": 126, "name": "Roller skates", "freebase_id": "/m/02p3w7d"},
        {"id": 127, "name": "Dagger", "freebase_id": "/m/02gzp"},
        {"id": 128, "name": "Seat belt", "freebase_id": "/m/0dkzw"},
        {"id": 129, "name": "Washing machine", "freebase_id": "/m/0174k2"},
        {"id": 130, "name": "Jet ski", "freebase_id": "/m/01xs3r"},
        {"id": 131, "name": "Sombrero", "freebase_id": "/m/02jfl0"},
        {"id": 132, "name": "Pig", "freebase_id": "/m/068zj"},
        {"id": 133, "name": "Drinking straw", "freebase_id": "/m/03v5tg"},
        {"id": 134, "name": "Peach", "freebase_id": "/m/0dj6p"},
        {"id": 135, "name": "Tortoise", "freebase_id": "/m/011k07"},
        {"id": 136, "name": "Towel", "freebase_id": "/m/0162_1"},
        {"id": 137, "name": "Tablet computer", "freebase_id": "/m/0bh9flk"},
        {"id": 138, "name": "Cucumber", "freebase_id": "/m/015x4r"},
        {"id": 139, "name": "Mule", "freebase_id": "/m/0dbzx"},
        {"id": 140, "name": "Potato", "freebase_id": "/m/05vtc"},
        {"id": 141, "name": "Frog", "freebase_id": "/m/09ld4"},
        {"id": 142, "name": "Bear", "freebase_id": "/m/01dws"},
        {"id": 143, "name": "Lighthouse", "freebase_id": "/m/04h7h"},
        {"id": 144, "name": "Belt", "freebase_id": "/m/0176mf"},
        {"id": 145, "name": "Baseball bat", "freebase_id": "/m/03g8mr"},
        {"id": 146, "name": "Racket", "freebase_id": "/m/0dv9c"},
        {"id": 147, "name": "Sword", "freebase_id": "/m/06y5r"},
        {"id": 148, "name": "Bagel", "freebase_id": "/m/01fb_0"},
        {"id": 149, "name": "Goat", "freebase_id": "/m/03fwl"},
        {"id": 150, "name": "Lizard", "freebase_id": "/m/04m9y"},
        {"id": 151, "name": "Parrot", "freebase_id": "/m/0gv1x"},
        {"id": 152, "name": "Owl", "freebase_id": "/m/09d5_"},
        {"id": 153, "name": "Turkey", "freebase_id": "/m/0jly1"},
        {"id": 154, "name": "Cello", "freebase_id": "/m/01xqw"},
        {"id": 155, "name": "Knife", "freebase_id": "/m/04ctx"},
        {"id": 156, "name": "Handgun", "freebase_id": "/m/0gxl3"},
        {"id": 157, "name": "Carrot", "freebase_id": "/m/0fj52s"},
        {"id": 158, "name": "Hamburger", "freebase_id": "/m/0cdn1"},
        {"id": 159, "name": "Grapefruit", "freebase_id": "/m/0hqkz"},
        {"id": 160, "name": "Tap", "freebase_id": "/m/02jz0l"},
        {"id": 161, "name": "Tea", "freebase_id": "/m/07clx"},
        {"id": 162, "name": "Bull", "freebase_id": "/m/0cnyhnx"},
        {"id": 163, "name": "Turtle", "freebase_id": "/m/09dzg"},
        {"id": 164, "name": "Bust", "freebase_id": "/m/04yqq2"},
        {"id": 165, "name": "Monkey", "freebase_id": "/m/08pbxl"},
        {"id": 166, "name": "Wok", "freebase_id": "/m/084rd"},
        {"id": 167, "name": "Broccoli", "freebase_id": "/m/0hkxq"},
        {"id": 168, "name": "Pitcher", "freebase_id": "/m/054fyh"},
        {"id": 169, "name": "Whiteboard", "freebase_id": "/m/02d9qx"},
        {"id": 170, "name": "Squirrel", "freebase_id": "/m/071qp"},
        {"id": 171, "name": "Jug", "freebase_id": "/m/08hvt4"},
        {"id": 172, "name": "Woodpecker", "freebase_id": "/m/01dy8n"},
        {"id": 173, "name": "Pizza", "freebase_id": "/m/0663v"},
        {"id": 174, "name": "Surfboard", "freebase_id": "/m/019w40"},
        {"id": 175, "name": "Sofa bed", "freebase_id": "/m/03m3pdh"},
        {"id": 176, "name": "Sheep", "freebase_id": "/m/07bgp"},
        {"id": 177, "name": "Candle", "freebase_id": "/m/0c06p"},
        {"id": 178, "name": "Muffin", "freebase_id": "/m/01tcjp"},
        {"id": 179, "name": "Cookie", "freebase_id": "/m/021mn"},
        {"id": 180, "name": "Apple", "freebase_id": "/m/014j1m"},
        {"id": 181, "name": "Chest of drawers", "freebase_id": "/m/05kyg_"},
        {"id": 182, "name": "Skull", "freebase_id": "/m/016m2d"},
        {"id": 183, "name": "Chicken", "freebase_id": "/m/09b5t"},
        {"id": 184, "name": "Loveseat", "freebase_id": "/m/0703r8"},
        {"id": 185, "name": "Baseball glove", "freebase_id": "/m/03grzl"},
        {"id": 186, "name": "Piano", "freebase_id": "/m/05r5c"},
        {"id": 187, "name": "Waste container", "freebase_id": "/m/0bjyj5"},
        {"id": 188, "name": "Barrel", "freebase_id": "/m/02zn6n"},
        {"id": 189, "name": "Swan", "freebase_id": "/m/0dftk"},
        {"id": 190, "name": "Taxi", "freebase_id": "/m/0pg52"},
        {"id": 191, "name": "Lemon", "freebase_id": "/m/09k_b"},
        {"id": 192, "name": "Pumpkin", "freebase_id": "/m/05zsy"},
        {"id": 193, "name": "Sparrow", "freebase_id": "/m/0h23m"},
        {"id": 194, "name": "Orange", "freebase_id": "/m/0cyhj_"},
        {"id": 195, "name": "Tank", "freebase_id": "/m/07cmd"},
        {"id": 196, "name": "Sandwich", "freebase_id": "/m/0l515"},
        {"id": 197, "name": "Coffee", "freebase_id": "/m/02vqfm"},
        {"id": 198, "name": "Juice", "freebase_id": "/m/01z1kdw"},
        {"id": 199, "name": "Coin", "freebase_id": "/m/0242l"},
        {"id": 200, "name": "Pen", "freebase_id": "/m/0k1tl"},
        {"id": 201, "name": "Watch", "freebase_id": "/m/0gjkl"},
        {"id": 202, "name": "Eagle", "freebase_id": "/m/09csl"},
        {"id": 203, "name": "Goose", "freebase_id": "/m/0dbvp"},
        {"id": 204, "name": "Falcon", "freebase_id": "/m/0f6wt"},
        {"id": 205, "name": "Christmas tree", "freebase_id": "/m/025nd"},
        {"id": 206, "name": "Sunflower", "freebase_id": "/m/0ftb8"},
        {"id": 207, "name": "Vase", "freebase_id": "/m/02s195"},
        {"id": 208, "name": "Football", "freebase_id": "/m/01226z"},
        {"id": 209, "name": "Canoe", "freebase_id": "/m/0ph39"},
        {"id": 210, "name": "High heels", "freebase_id": "/m/06k2mb"},
        {"id": 211, "name": "Spoon", "freebase_id": "/m/0cmx8"},
        {"id": 212, "name": "Mug", "freebase_id": "/m/02jvh9"},
        {"id": 213, "name": "Swimwear", "freebase_id": "/m/01gkx_"},
        {"id": 214, "name": "Duck", "freebase_id": "/m/09ddx"},
        {"id": 215, "name": "Cat", "freebase_id": "/m/01yrx"},
        {"id": 216, "name": "Tomato", "freebase_id": "/m/07j87"},
        {"id": 217, "name": "Cocktail", "freebase_id": "/m/024g6"},
        {"id": 218, "name": "Clock", "freebase_id": "/m/01x3z"},
        {"id": 219, "name": "Cowboy hat", "freebase_id": "/m/025rp__"},
        {"id": 220, "name": "Miniskirt", "freebase_id": "/m/01cmb2"},
        {"id": 221, "name": "Cattle", "freebase_id": "/m/01xq0k1"},
        {"id": 222, "name": "Strawberry", "freebase_id": "/m/07fbm7"},
        {"id": 223, "name": "Bronze sculpture", "freebase_id": "/m/01yx86"},
        {"id": 224, "name": "Pillow", "freebase_id": "/m/034c16"},
        {"id": 225, "name": "Squash", "freebase_id": "/m/0dv77"},
        {"id": 226, "name": "Traffic light", "freebase_id": "/m/015qff"},
        {"id": 227, "name": "Saucer", "freebase_id": "/m/03q5c7"},
        {"id": 228, "name": "Reptile", "freebase_id": "/m/06bt6"},
        {"id": 229, "name": "Cake", "freebase_id": "/m/0fszt"},
        {"id": 230, "name": "Plastic bag", "freebase_id": "/m/05gqfk"},
        {"id": 231, "name": "Studio couch", "freebase_id": "/m/026qbn5"},
        {"id": 232, "name": "Beer", "freebase_id": "/m/01599"},
        {"id": 233, "name": "Scarf", "freebase_id": "/m/02h19r"},
        {"id": 234, "name": "Coffee cup", "freebase_id": "/m/02p5f1q"},
        {"id": 235, "name": "Wine", "freebase_id": "/m/081qc"},
        {"id": 236, "name": "Mushroom", "freebase_id": "/m/052sf"},
        {"id": 237, "name": "Traffic sign", "freebase_id": "/m/01mqdt"},
        {"id": 238, "name": "Camera", "freebase_id": "/m/0dv5r"},
        {"id": 239, "name": "Rose", "freebase_id": "/m/06m11"},
        {"id": 240, "name": "Couch", "freebase_id": "/m/02crq1"},
        {"id": 241, "name": "Handbag", "freebase_id": "/m/080hkjn"},
        {"id": 242, "name": "Fedora", "freebase_id": "/m/02fq_6"},
        {"id": 243, "name": "Sock", "freebase_id": "/m/01nq26"},
        {"id": 244, "name": "Computer keyboard", "freebase_id": "/m/01m2v"},
        {"id": 245, "name": "Mobile phone", "freebase_id": "/m/050k8"},
        {"id": 246, "name": "Ball", "freebase_id": "/m/018xm"},
        {"id": 247, "name": "Balloon", "freebase_id": "/m/01j51"},
        {"id": 248, "name": "Horse", "freebase_id": "/m/03k3r"},
        {"id": 249, "name": "Boot", "freebase_id": "/m/01b638"},
        {"id": 250, "name": "Fish", "freebase_id": "/m/0ch_cf"},
        {"id": 251, "name": "Backpack", "freebase_id": "/m/01940j"},
        {"id": 252, "name": "Skirt", "freebase_id": "/m/02wv6h6"},
        {"id": 253, "name": "Van", "freebase_id": "/m/0h2r6"},
        {"id": 254, "name": "Bread", "freebase_id": "/m/09728"},
        {"id": 255, "name": "Glove", "freebase_id": "/m/0174n1"},
        {"id": 256, "name": "Dog", "freebase_id": "/m/0bt9lr"},
        {"id": 257, "name": "Airplane", "freebase_id": "/m/0cmf2"},
        {"id": 258, "name": "Motorcycle", "freebase_id": "/m/04_sv"},
        {"id": 259, "name": "Drink", "freebase_id": "/m/0271t"},
        {"id": 260, "name": "Book", "freebase_id": "/m/0bt_c3"},
        {"id": 261, "name": "Train", "freebase_id": "/m/07jdr"},
        {"id": 262, "name": "Flower", "freebase_id": "/m/0c9ph5"},
        {"id": 263, "name": "Carnivore", "freebase_id": "/m/01lrl"},
        {"id": 264, "name": "Human ear", "freebase_id": "/m/039xj_"},
        {"id": 265, "name": "Toy", "freebase_id": "/m/0138tl"},
        {"id": 266, "name": "Box", "freebase_id": "/m/025dyy"},
        {"id": 267, "name": "Truck", "freebase_id": "/m/07r04"},
        {"id": 268, "name": "Wheel", "freebase_id": "/m/083wq"},
        {"id": 269, "name": "Aircraft", "freebase_id": "/m/0k5j"},
        {"id": 270, "name": "Bus", "freebase_id": "/m/01bjv"},
        {"id": 271, "name": "Human mouth", "freebase_id": "/m/0283dt1"},
        {"id": 272, "name": "Sculpture", "freebase_id": "/m/06msq"},
        {"id": 273, "name": "Shirt", "freebase_id": "/m/01n4qj"},
        {"id": 274, "name": "Hat", "freebase_id": "/m/02dl1y"},
        {"id": 275, "name": "Vehicle registration plate", "freebase_id": "/m/01jfm_"},
        {"id": 276, "name": "Guitar", "freebase_id": "/m/0342h"},
        {"id": 277, "name": "Sun hat", "freebase_id": "/m/02wbtzl"},
        {"id": 278, "name": "Bottle", "freebase_id": "/m/04dr76w"},
        {"id": 279, "name": "Luggage and bags", "freebase_id": "/m/0hf58v5"},
        {"id": 280, "name": "Trousers", "freebase_id": "/m/07mhn"},
        {"id": 281, "name": "Bicycle wheel", "freebase_id": "/m/01bqk0"},
        {"id": 282, "name": "Suit", "freebase_id": "/m/01xyhv"},
        {"id": 283, "name": "Bowl", "freebase_id": "/m/04kkgm"},
        {"id": 284, "name": "Man", "freebase_id": "/m/04yx4"},
        {"id": 285, "name": "Flowerpot", "freebase_id": "/m/0fm3zh"},
        {"id": 286, "name": "Laptop", "freebase_id": "/m/01c648"},
        {"id": 287, "name": "Boy", "freebase_id": "/m/01bl7v"},
        {"id": 288, "name": "Picture frame", "freebase_id": "/m/06z37_"},
        {"id": 289, "name": "Bird", "freebase_id": "/m/015p6"},
        {"id": 290, "name": "Car", "freebase_id": "/m/0k4j"},
        {"id": 291, "name": "Shorts", "freebase_id": "/m/01bfm9"},
        {"id": 292, "name": "Woman", "freebase_id": "/m/03bt1vf"},
        {"id": 293, "name": "Platter", "freebase_id": "/m/099ssp"},
        {"id": 294, "name": "Tie", "freebase_id": "/m/01rkbr"},
        {"id": 295, "name": "Girl", "freebase_id": "/m/05r655"},
        {"id": 296, "name": "Skyscraper", "freebase_id": "/m/079cl"},
        {"id": 297, "name": "Person", "freebase_id": "/m/01g317"},
        {"id": 298, "name": "Flag", "freebase_id": "/m/03120"},
        {"id": 299, "name": "Jeans", "freebase_id": "/m/0fly7"},
        {"id": 300, "name": "Dress", "freebase_id": "/m/01d40f"},
    ],
    "kitti": [
        {"id": 24, "name": "person"},
        {"id": 25, "name": "rider"},
        {"id": 26, "name": "car"},
        {"id": 27, "name": "truck"},
        {"id": 28, "name": "bus"},
        {"id": 31, "name": "train"},
        {"id": 32, "name": "motorcycle"},
        {"id": 33, "name": "bicycle"},
    ],
    "wilddash": [
        {"id": 1, "name": "ego vehicle"},
        {"id": 24, "name": "person"},
        {"id": 25, "name": "rider"},
        {"id": 26, "name": "car"},
        {"id": 27, "name": "truck"},
        {"id": 28, "name": "bus"},
        {"id": 29, "name": "caravan"},
        {"id": 30, "name": "trailer"},
        {"id": 31, "name": "train"},
        {"id": 32, "name": "motorcycle"},
        {"id": 33, "name": "bicycle"},
        {"id": 34, "name": "pickup"},
        {"id": 35, "name": "van"},
    ],
}
