import os

from detectron2.data import DatasetCatalog, MetadataCatalog
from detectron2.data.datasets import load_sem_seg

from .coco import custom_register_coco_instances

PASCAL_CTX_59_CATEGORIES = [
    {"color": [180, 120, 120], "id": 0, "isthing": 0, "name": "aeroplane"},
    {"color": [6, 230, 230], "id": 1, "isthing": 0, "name": "bag"},
    {"color": [80, 50, 50], "id": 2, "isthing": 0, "name": "bed"},
    {"color": [4, 200, 3], "id": 3, "isthing": 0, "name": "bedclothes"},
    {"color": [120, 120, 80], "id": 4, "isthing": 0, "name": "bench"},
    {"color": [140, 140, 140], "id": 5, "isthing": 0, "name": "bicycle"},
    {"color": [204, 5, 255], "id": 6, "isthing": 0, "name": "bird"},
    {"color": [230, 230, 230], "id": 7, "isthing": 0, "name": "boat"},
    {"color": [4, 250, 7], "id": 8, "isthing": 0, "name": "book"},
    {"color": [224, 5, 255], "id": 9, "isthing": 0, "name": "bottle"},
    {"color": [235, 255, 7], "id": 10, "isthing": 0, "name": "building"},
    {"color": [150, 5, 61], "id": 11, "isthing": 0, "name": "bus"},
    {"color": [120, 120, 70], "id": 12, "isthing": 0, "name": "cabinet"},
    {"color": [8, 255, 51], "id": 13, "isthing": 0, "name": "car"},
    {"color": [255, 6, 82], "id": 14, "isthing": 0, "name": "cat"},
    {"color": [143, 255, 140], "id": 15, "isthing": 0, "name": "ceiling"},
    {"color": [204, 255, 4], "id": 16, "isthing": 0, "name": "chair"},
    {"color": [255, 51, 7], "id": 17, "isthing": 0, "name": "cloth"},
    {"color": [204, 70, 3], "id": 18, "isthing": 0, "name": "computer"},
    {"color": [0, 102, 200], "id": 19, "isthing": 0, "name": "cow"},
    {"color": [61, 230, 250], "id": 20, "isthing": 0, "name": "cup"},
    {"color": [255, 6, 51], "id": 21, "isthing": 0, "name": "curtain"},
    {"color": [11, 102, 255], "id": 22, "isthing": 0, "name": "dog"},
    {"color": [255, 7, 71], "id": 23, "isthing": 0, "name": "door"},
    {"color": [255, 9, 224], "id": 24, "isthing": 0, "name": "fence"},
    {"color": [9, 7, 230], "id": 25, "isthing": 0, "name": "floor"},
    {"color": [220, 220, 220], "id": 26, "isthing": 0, "name": "flower"},
    {"color": [255, 9, 92], "id": 27, "isthing": 0, "name": "food"},
    {"color": [112, 9, 255], "id": 28, "isthing": 0, "name": "grass"},
    {"color": [8, 255, 214], "id": 29, "isthing": 0, "name": "ground"},
    {"color": [7, 255, 224], "id": 30, "isthing": 0, "name": "horse"},
    {"color": [255, 184, 6], "id": 31, "isthing": 0, "name": "keyboard"},
    {"color": [10, 255, 71], "id": 32, "isthing": 0, "name": "light"},
    {"color": [255, 41, 10], "id": 33, "isthing": 0, "name": "motorbike"},
    {"color": [7, 255, 255], "id": 34, "isthing": 0, "name": "mountain"},
    {"color": [224, 255, 8], "id": 35, "isthing": 0, "name": "mouse"},
    {"color": [102, 8, 255], "id": 36, "isthing": 0, "name": "person"},
    {"color": [255, 61, 6], "id": 37, "isthing": 0, "name": "plate"},
    {"color": [255, 194, 7], "id": 38, "isthing": 0, "name": "platform"},
    {"color": [255, 122, 8], "id": 39, "isthing": 0, "name": "pottedplant"},
    {"color": [0, 255, 20], "id": 40, "isthing": 0, "name": "road"},
    {"color": [255, 8, 41], "id": 41, "isthing": 0, "name": "rock"},
    {"color": [255, 5, 153], "id": 42, "isthing": 0, "name": "sheep"},
    {"color": [6, 51, 255], "id": 43, "isthing": 0, "name": "shelves"},
    {"color": [235, 12, 255], "id": 44, "isthing": 0, "name": "sidewalk"},
    {"color": [160, 150, 20], "id": 45, "isthing": 0, "name": "sign"},
    {"color": [0, 163, 255], "id": 46, "isthing": 0, "name": "sky"},
    {"color": [140, 140, 140], "id": 47, "isthing": 0, "name": "snow"},
    {"color": [250, 10, 15], "id": 48, "isthing": 0, "name": "sofa"},
    {"color": [20, 255, 0], "id": 49, "isthing": 0, "name": "diningtable"},
    {"color": [31, 255, 0], "id": 50, "isthing": 0, "name": "track"},
    {"color": [255, 31, 0], "id": 51, "isthing": 0, "name": "train"},
    {"color": [255, 224, 0], "id": 52, "isthing": 0, "name": "tree"},
    {"color": [153, 255, 0], "id": 53, "isthing": 0, "name": "truck"},
    {"color": [0, 0, 255], "id": 54, "isthing": 0, "name": "tvmonitor"},
    {"color": [255, 71, 0], "id": 55, "isthing": 0, "name": "wall"},
    {"color": [0, 235, 255], "id": 56, "isthing": 0, "name": "water"},
    {"color": [0, 173, 255], "id": 57, "isthing": 0, "name": "window"},
    {"color": [31, 0, 255], "id": 58, "isthing": 0, "name": "wood"},
]

PASCAL_CTX_459_CATEGORIES = [
    {"color": [120, 120, 120], "id": 0, "isthing": 0, "name": "accordion"},
    {"color": [180, 120, 120], "id": 1, "isthing": 0, "name": "aeroplane"},
    {"color": [6, 230, 230], "id": 2, "isthing": 0, "name": "air conditioner"},
    {"color": [80, 50, 50], "id": 3, "isthing": 0, "name": "antenna"},
    {"color": [4, 200, 3], "id": 4, "isthing": 0, "name": "artillery"},
    {"color": [120, 120, 80], "id": 5, "isthing": 0, "name": "ashtray"},
    {"color": [140, 140, 140], "id": 6, "isthing": 0, "name": "atrium"},
    {"color": [204, 5, 255], "id": 7, "isthing": 0, "name": "baby carriage"},
    {"color": [230, 230, 230], "id": 8, "isthing": 0, "name": "bag"},
    {"color": [4, 250, 7], "id": 9, "isthing": 0, "name": "ball"},
    {"color": [224, 5, 255], "id": 10, "isthing": 0, "name": "balloon"},
    {"color": [235, 255, 7], "id": 11, "isthing": 0, "name": "bamboo weaving"},
    {"color": [150, 5, 61], "id": 12, "isthing": 0, "name": "barrel"},
    {"color": [120, 120, 70], "id": 13, "isthing": 0, "name": "baseball bat"},
    {"color": [8, 255, 51], "id": 14, "isthing": 0, "name": "basket"},
    {"color": [255, 6, 82], "id": 15, "isthing": 0, "name": "basketball backboard"},
    {"color": [143, 255, 140], "id": 16, "isthing": 0, "name": "bathtub"},
    {"color": [204, 255, 4], "id": 17, "isthing": 0, "name": "bed"},
    {"color": [255, 51, 7], "id": 18, "isthing": 0, "name": "bedclothes"},
    {"color": [204, 70, 3], "id": 19, "isthing": 0, "name": "beer"},
    {"color": [0, 102, 200], "id": 20, "isthing": 0, "name": "bell"},
    {"color": [61, 230, 250], "id": 21, "isthing": 0, "name": "bench"},
    {"color": [255, 6, 51], "id": 22, "isthing": 0, "name": "bicycle"},
    {"color": [11, 102, 255], "id": 23, "isthing": 0, "name": "binoculars"},
    {"color": [255, 7, 71], "id": 24, "isthing": 0, "name": "bird"},
    {"color": [255, 9, 224], "id": 25, "isthing": 0, "name": "bird cage"},
    {"color": [9, 7, 230], "id": 26, "isthing": 0, "name": "bird feeder"},
    {"color": [220, 220, 220], "id": 27, "isthing": 0, "name": "bird nest"},
    {"color": [255, 9, 92], "id": 28, "isthing": 0, "name": "blackboard"},
    {"color": [112, 9, 255], "id": 29, "isthing": 0, "name": "board"},
    {"color": [8, 255, 214], "id": 30, "isthing": 0, "name": "boat"},
    {"color": [7, 255, 224], "id": 31, "isthing": 0, "name": "bone"},
    {"color": [255, 184, 6], "id": 32, "isthing": 0, "name": "book"},
    {"color": [10, 255, 71], "id": 33, "isthing": 0, "name": "bottle"},
    {"color": [255, 41, 10], "id": 34, "isthing": 0, "name": "bottle opener"},
    {"color": [7, 255, 255], "id": 35, "isthing": 0, "name": "bowl"},
    {"color": [224, 255, 8], "id": 36, "isthing": 0, "name": "box"},
    {"color": [102, 8, 255], "id": 37, "isthing": 0, "name": "bracelet"},
    {"color": [255, 61, 6], "id": 38, "isthing": 0, "name": "brick"},
    {"color": [255, 194, 7], "id": 39, "isthing": 0, "name": "bridge"},
    {"color": [255, 122, 8], "id": 40, "isthing": 0, "name": "broom"},
    {"color": [0, 255, 20], "id": 41, "isthing": 0, "name": "brush"},
    {"color": [255, 8, 41], "id": 42, "isthing": 0, "name": "bucket"},
    {"color": [255, 5, 153], "id": 43, "isthing": 0, "name": "building"},
    {"color": [6, 51, 255], "id": 44, "isthing": 0, "name": "bus"},
    {"color": [235, 12, 255], "id": 45, "isthing": 0, "name": "cabinet"},
    {"color": [160, 150, 20], "id": 46, "isthing": 0, "name": "cabinet door"},
    {"color": [0, 163, 255], "id": 47, "isthing": 0, "name": "cage"},
    {"color": [140, 140, 140], "id": 48, "isthing": 0, "name": "cake"},
    {"color": [250, 10, 15], "id": 49, "isthing": 0, "name": "calculator"},
    {"color": [20, 255, 0], "id": 50, "isthing": 0, "name": "calendar"},
    {"color": [31, 255, 0], "id": 51, "isthing": 0, "name": "camel"},
    {"color": [255, 31, 0], "id": 52, "isthing": 0, "name": "camera"},
    {"color": [255, 224, 0], "id": 53, "isthing": 0, "name": "camera lens"},
    {"color": [153, 255, 0], "id": 54, "isthing": 0, "name": "can"},
    {"color": [0, 0, 255], "id": 55, "isthing": 0, "name": "candle"},
    {"color": [255, 71, 0], "id": 56, "isthing": 0, "name": "candle holder"},
    {"color": [0, 235, 255], "id": 57, "isthing": 0, "name": "cap"},
    {"color": [0, 173, 255], "id": 58, "isthing": 0, "name": "car"},
    {"color": [31, 0, 255], "id": 59, "isthing": 0, "name": "card"},
    {"color": [120, 120, 120], "id": 60, "isthing": 0, "name": "cart"},
    {"color": [180, 120, 120], "id": 61, "isthing": 0, "name": "case"},
    {"color": [6, 230, 230], "id": 62, "isthing": 0, "name": "casette recorder"},
    {"color": [80, 50, 50], "id": 63, "isthing": 0, "name": "cash register"},
    {"color": [4, 200, 3], "id": 64, "isthing": 0, "name": "cat"},
    {"color": [120, 120, 80], "id": 65, "isthing": 0, "name": "cd"},
    {"color": [140, 140, 140], "id": 66, "isthing": 0, "name": "cd player"},
    {"color": [204, 5, 255], "id": 67, "isthing": 0, "name": "ceiling"},
    {"color": [230, 230, 230], "id": 68, "isthing": 0, "name": "cell phone"},
    {"color": [4, 250, 7], "id": 69, "isthing": 0, "name": "cello"},
    {"color": [224, 5, 255], "id": 70, "isthing": 0, "name": "chain"},
    {"color": [235, 255, 7], "id": 71, "isthing": 0, "name": "chair"},
    {"color": [150, 5, 61], "id": 72, "isthing": 0, "name": "chessboard"},
    {"color": [120, 120, 70], "id": 73, "isthing": 0, "name": "chicken"},
    {"color": [8, 255, 51], "id": 74, "isthing": 0, "name": "chopstick"},
    {"color": [255, 6, 82], "id": 75, "isthing": 0, "name": "clip"},
    {"color": [143, 255, 140], "id": 76, "isthing": 0, "name": "clippers"},
    {"color": [204, 255, 4], "id": 77, "isthing": 0, "name": "clock"},
    {"color": [255, 51, 7], "id": 78, "isthing": 0, "name": "closet"},
    {"color": [204, 70, 3], "id": 79, "isthing": 0, "name": "cloth"},
    {"color": [0, 102, 200], "id": 80, "isthing": 0, "name": "clothes tree"},
    {"color": [61, 230, 250], "id": 81, "isthing": 0, "name": "coffee"},
    {"color": [255, 6, 51], "id": 82, "isthing": 0, "name": "coffee machine"},
    {"color": [11, 102, 255], "id": 83, "isthing": 0, "name": "comb"},
    {"color": [255, 7, 71], "id": 84, "isthing": 0, "name": "computer"},
    {"color": [255, 9, 224], "id": 85, "isthing": 0, "name": "concrete"},
    {"color": [9, 7, 230], "id": 86, "isthing": 0, "name": "cone"},
    {"color": [220, 220, 220], "id": 87, "isthing": 0, "name": "container"},
    {"color": [255, 9, 92], "id": 88, "isthing": 0, "name": "control booth"},
    {"color": [112, 9, 255], "id": 89, "isthing": 0, "name": "controller"},
    {"color": [8, 255, 214], "id": 90, "isthing": 0, "name": "cooker"},
    {"color": [7, 255, 224], "id": 91, "isthing": 0, "name": "copying machine"},
    {"color": [255, 184, 6], "id": 92, "isthing": 0, "name": "coral"},
    {"color": [10, 255, 71], "id": 93, "isthing": 0, "name": "cork"},
    {"color": [255, 41, 10], "id": 94, "isthing": 0, "name": "corkscrew"},
    {"color": [7, 255, 255], "id": 95, "isthing": 0, "name": "counter"},
    {"color": [224, 255, 8], "id": 96, "isthing": 0, "name": "court"},
    {"color": [102, 8, 255], "id": 97, "isthing": 0, "name": "cow"},
    {"color": [255, 61, 6], "id": 98, "isthing": 0, "name": "crabstick"},
    {"color": [255, 194, 7], "id": 99, "isthing": 0, "name": "crane"},
    {"color": [255, 122, 8], "id": 100, "isthing": 0, "name": "crate"},
    {"color": [0, 255, 20], "id": 101, "isthing": 0, "name": "cross"},
    {"color": [255, 8, 41], "id": 102, "isthing": 0, "name": "crutch"},
    {"color": [255, 5, 153], "id": 103, "isthing": 0, "name": "cup"},
    {"color": [6, 51, 255], "id": 104, "isthing": 0, "name": "curtain"},
    {"color": [235, 12, 255], "id": 105, "isthing": 0, "name": "cushion"},
    {"color": [160, 150, 20], "id": 106, "isthing": 0, "name": "cutting board"},
    {"color": [0, 163, 255], "id": 107, "isthing": 0, "name": "dais"},
    {"color": [140, 140, 140], "id": 108, "isthing": 0, "name": "disc"},
    {"color": [250, 10, 15], "id": 109, "isthing": 0, "name": "disc case"},
    {"color": [20, 255, 0], "id": 110, "isthing": 0, "name": "dishwasher"},
    {"color": [31, 255, 0], "id": 111, "isthing": 0, "name": "dock"},
    {"color": [255, 31, 0], "id": 112, "isthing": 0, "name": "dog"},
    {"color": [255, 224, 0], "id": 113, "isthing": 0, "name": "dolphin"},
    {"color": [153, 255, 0], "id": 114, "isthing": 0, "name": "door"},
    {"color": [0, 0, 255], "id": 115, "isthing": 0, "name": "drainer"},
    {"color": [255, 71, 0], "id": 116, "isthing": 0, "name": "dray"},
    {"color": [0, 235, 255], "id": 117, "isthing": 0, "name": "drink dispenser"},
    {"color": [0, 173, 255], "id": 118, "isthing": 0, "name": "drinking machine"},
    {"color": [31, 0, 255], "id": 119, "isthing": 0, "name": "drop"},
    {"color": [120, 120, 120], "id": 120, "isthing": 0, "name": "drug"},
    {"color": [180, 120, 120], "id": 121, "isthing": 0, "name": "drum"},
    {"color": [6, 230, 230], "id": 122, "isthing": 0, "name": "drum kit"},
    {"color": [80, 50, 50], "id": 123, "isthing": 0, "name": "duck"},
    {"color": [4, 200, 3], "id": 124, "isthing": 0, "name": "dumbbell"},
    {"color": [120, 120, 80], "id": 125, "isthing": 0, "name": "earphone"},
    {"color": [140, 140, 140], "id": 126, "isthing": 0, "name": "earrings"},
    {"color": [204, 5, 255], "id": 127, "isthing": 0, "name": "egg"},
    {"color": [230, 230, 230], "id": 128, "isthing": 0, "name": "electric fan"},
    {"color": [4, 250, 7], "id": 129, "isthing": 0, "name": "electric iron"},
    {"color": [224, 5, 255], "id": 130, "isthing": 0, "name": "electric pot"},
    {"color": [235, 255, 7], "id": 131, "isthing": 0, "name": "electric saw"},
    {"color": [150, 5, 61], "id": 132, "isthing": 0, "name": "electronic keyboard"},
    {"color": [120, 120, 70], "id": 133, "isthing": 0, "name": "engine"},
    {"color": [8, 255, 51], "id": 134, "isthing": 0, "name": "envelope"},
    {"color": [255, 6, 82], "id": 135, "isthing": 0, "name": "equipment"},
    {"color": [143, 255, 140], "id": 136, "isthing": 0, "name": "escalator"},
    {"color": [204, 255, 4], "id": 137, "isthing": 0, "name": "exhibition booth"},
    {"color": [255, 51, 7], "id": 138, "isthing": 0, "name": "extinguisher"},
    {"color": [204, 70, 3], "id": 139, "isthing": 0, "name": "eyeglass"},
    {"color": [0, 102, 200], "id": 140, "isthing": 0, "name": "fan"},
    {"color": [61, 230, 250], "id": 141, "isthing": 0, "name": "faucet"},
    {"color": [255, 6, 51], "id": 142, "isthing": 0, "name": "fax machine"},
    {"color": [11, 102, 255], "id": 143, "isthing": 0, "name": "fence"},
    {"color": [255, 7, 71], "id": 144, "isthing": 0, "name": "ferris wheel"},
    {"color": [255, 9, 224], "id": 145, "isthing": 0, "name": "fire extinguisher"},
    {"color": [9, 7, 230], "id": 146, "isthing": 0, "name": "fire hydrant"},
    {"color": [220, 220, 220], "id": 147, "isthing": 0, "name": "fire place"},
    {"color": [255, 9, 92], "id": 148, "isthing": 0, "name": "fish"},
    {"color": [112, 9, 255], "id": 149, "isthing": 0, "name": "fish tank"},
    {"color": [8, 255, 214], "id": 150, "isthing": 0, "name": "fishbowl"},
    {"color": [7, 255, 224], "id": 151, "isthing": 0, "name": "fishing net"},
    {"color": [255, 184, 6], "id": 152, "isthing": 0, "name": "fishing pole"},
    {"color": [10, 255, 71], "id": 153, "isthing": 0, "name": "flag"},
    {"color": [255, 41, 10], "id": 154, "isthing": 0, "name": "flagstaff"},
    {"color": [7, 255, 255], "id": 155, "isthing": 0, "name": "flame"},
    {"color": [224, 255, 8], "id": 156, "isthing": 0, "name": "flashlight"},
    {"color": [102, 8, 255], "id": 157, "isthing": 0, "name": "floor"},
    {"color": [255, 61, 6], "id": 158, "isthing": 0, "name": "flower"},
    {"color": [255, 194, 7], "id": 159, "isthing": 0, "name": "fly"},
    {"color": [255, 122, 8], "id": 160, "isthing": 0, "name": "foam"},
    {"color": [0, 255, 20], "id": 161, "isthing": 0, "name": "food"},
    {"color": [255, 8, 41], "id": 162, "isthing": 0, "name": "footbridge"},
    {"color": [255, 5, 153], "id": 163, "isthing": 0, "name": "forceps"},
    {"color": [6, 51, 255], "id": 164, "isthing": 0, "name": "fork"},
    {"color": [235, 12, 255], "id": 165, "isthing": 0, "name": "forklift"},
    {"color": [160, 150, 20], "id": 166, "isthing": 0, "name": "fountain"},
    {"color": [0, 163, 255], "id": 167, "isthing": 0, "name": "fox"},
    {"color": [140, 140, 140], "id": 168, "isthing": 0, "name": "frame"},
    {"color": [250, 10, 15], "id": 169, "isthing": 0, "name": "fridge"},
    {"color": [20, 255, 0], "id": 170, "isthing": 0, "name": "frog"},
    {"color": [31, 255, 0], "id": 171, "isthing": 0, "name": "fruit"},
    {"color": [255, 31, 0], "id": 172, "isthing": 0, "name": "funnel"},
    {"color": [255, 224, 0], "id": 173, "isthing": 0, "name": "furnace"},
    {"color": [153, 255, 0], "id": 174, "isthing": 0, "name": "game controller"},
    {"color": [0, 0, 255], "id": 175, "isthing": 0, "name": "game machine"},
    {"color": [255, 71, 0], "id": 176, "isthing": 0, "name": "gas cylinder"},
    {"color": [0, 235, 255], "id": 177, "isthing": 0, "name": "gas hood"},
    {"color": [0, 173, 255], "id": 178, "isthing": 0, "name": "gas stove"},
    {"color": [31, 0, 255], "id": 179, "isthing": 0, "name": "gift box"},
    {"color": [120, 120, 120], "id": 180, "isthing": 0, "name": "glass"},
    {"color": [180, 120, 120], "id": 181, "isthing": 0, "name": "glass marble"},
    {"color": [6, 230, 230], "id": 182, "isthing": 0, "name": "globe"},
    {"color": [80, 50, 50], "id": 183, "isthing": 0, "name": "glove"},
    {"color": [4, 200, 3], "id": 184, "isthing": 0, "name": "goal"},
    {"color": [120, 120, 80], "id": 185, "isthing": 0, "name": "grandstand"},
    {"color": [140, 140, 140], "id": 186, "isthing": 0, "name": "grass"},
    {"color": [204, 5, 255], "id": 187, "isthing": 0, "name": "gravestone"},
    {"color": [230, 230, 230], "id": 188, "isthing": 0, "name": "ground"},
    {"color": [4, 250, 7], "id": 189, "isthing": 0, "name": "guardrail"},
    {"color": [224, 5, 255], "id": 190, "isthing": 0, "name": "guitar"},
    {"color": [235, 255, 7], "id": 191, "isthing": 0, "name": "gun"},
    {"color": [150, 5, 61], "id": 192, "isthing": 0, "name": "hammer"},
    {"color": [120, 120, 70], "id": 193, "isthing": 0, "name": "hand cart"},
    {"color": [8, 255, 51], "id": 194, "isthing": 0, "name": "handle"},
    {"color": [255, 6, 82], "id": 195, "isthing": 0, "name": "handrail"},
    {"color": [143, 255, 140], "id": 196, "isthing": 0, "name": "hanger"},
    {"color": [204, 255, 4], "id": 197, "isthing": 0, "name": "hard disk drive"},
    {"color": [255, 51, 7], "id": 198, "isthing": 0, "name": "hat"},
    {"color": [204, 70, 3], "id": 199, "isthing": 0, "name": "hay"},
    {"color": [0, 102, 200], "id": 200, "isthing": 0, "name": "headphone"},
    {"color": [61, 230, 250], "id": 201, "isthing": 0, "name": "heater"},
    {"color": [255, 6, 51], "id": 202, "isthing": 0, "name": "helicopter"},
    {"color": [11, 102, 255], "id": 203, "isthing": 0, "name": "helmet"},
    {"color": [255, 7, 71], "id": 204, "isthing": 0, "name": "holder"},
    {"color": [255, 9, 224], "id": 205, "isthing": 0, "name": "hook"},
    {"color": [9, 7, 230], "id": 206, "isthing": 0, "name": "horse"},
    {"color": [220, 220, 220], "id": 207, "isthing": 0, "name": "horse-drawn carriage"},
    {"color": [255, 9, 92], "id": 208, "isthing": 0, "name": "hot-air balloon"},
    {"color": [112, 9, 255], "id": 209, "isthing": 0, "name": "hydrovalve"},
    {"color": [8, 255, 214], "id": 210, "isthing": 0, "name": "ice"},
    {"color": [7, 255, 224], "id": 211, "isthing": 0, "name": "inflator pump"},
    {"color": [255, 184, 6], "id": 212, "isthing": 0, "name": "ipod"},
    {"color": [10, 255, 71], "id": 213, "isthing": 0, "name": "iron"},
    {"color": [255, 41, 10], "id": 214, "isthing": 0, "name": "ironing board"},
    {"color": [7, 255, 255], "id": 215, "isthing": 0, "name": "jar"},
    {"color": [224, 255, 8], "id": 216, "isthing": 0, "name": "kart"},
    {"color": [102, 8, 255], "id": 217, "isthing": 0, "name": "kettle"},
    {"color": [255, 61, 6], "id": 218, "isthing": 0, "name": "key"},
    {"color": [255, 194, 7], "id": 219, "isthing": 0, "name": "keyboard"},
    {"color": [255, 122, 8], "id": 220, "isthing": 0, "name": "kitchen range"},
    {"color": [0, 255, 20], "id": 221, "isthing": 0, "name": "kite"},
    {"color": [255, 8, 41], "id": 222, "isthing": 0, "name": "knife"},
    {"color": [255, 5, 153], "id": 223, "isthing": 0, "name": "knife block"},
    {"color": [6, 51, 255], "id": 224, "isthing": 0, "name": "ladder"},
    {"color": [235, 12, 255], "id": 225, "isthing": 0, "name": "ladder truck"},
    {"color": [160, 150, 20], "id": 226, "isthing": 0, "name": "ladle"},
    {"color": [0, 163, 255], "id": 227, "isthing": 0, "name": "laptop"},
    {"color": [140, 140, 140], "id": 228, "isthing": 0, "name": "leaves"},
    {"color": [250, 10, 15], "id": 229, "isthing": 0, "name": "lid"},
    {"color": [20, 255, 0], "id": 230, "isthing": 0, "name": "life buoy"},
    {"color": [31, 255, 0], "id": 231, "isthing": 0, "name": "light"},
    {"color": [255, 31, 0], "id": 232, "isthing": 0, "name": "light bulb"},
    {"color": [255, 224, 0], "id": 233, "isthing": 0, "name": "lighter"},
    {"color": [153, 255, 0], "id": 234, "isthing": 0, "name": "line"},
    {"color": [0, 0, 255], "id": 235, "isthing": 0, "name": "lion"},
    {"color": [255, 71, 0], "id": 236, "isthing": 0, "name": "lobster"},
    {"color": [0, 235, 255], "id": 237, "isthing": 0, "name": "lock"},
    {"color": [0, 173, 255], "id": 238, "isthing": 0, "name": "machine"},
    {"color": [31, 0, 255], "id": 239, "isthing": 0, "name": "mailbox"},
    {"color": [120, 120, 120], "id": 240, "isthing": 0, "name": "mannequin"},
    {"color": [180, 120, 120], "id": 241, "isthing": 0, "name": "map"},
    {"color": [6, 230, 230], "id": 242, "isthing": 0, "name": "mask"},
    {"color": [80, 50, 50], "id": 243, "isthing": 0, "name": "mat"},
    {"color": [4, 200, 3], "id": 244, "isthing": 0, "name": "match book"},
    {"color": [120, 120, 80], "id": 245, "isthing": 0, "name": "mattress"},
    {"color": [140, 140, 140], "id": 246, "isthing": 0, "name": "menu"},
    {"color": [204, 5, 255], "id": 247, "isthing": 0, "name": "metal"},
    {"color": [230, 230, 230], "id": 248, "isthing": 0, "name": "meter box"},
    {"color": [4, 250, 7], "id": 249, "isthing": 0, "name": "microphone"},
    {"color": [224, 5, 255], "id": 250, "isthing": 0, "name": "microwave"},
    {"color": [235, 255, 7], "id": 251, "isthing": 0, "name": "mirror"},
    {"color": [150, 5, 61], "id": 252, "isthing": 0, "name": "missile"},
    {"color": [120, 120, 70], "id": 253, "isthing": 0, "name": "model"},
    {"color": [8, 255, 51], "id": 254, "isthing": 0, "name": "money"},
    {"color": [255, 6, 82], "id": 255, "isthing": 0, "name": "monkey"},
    {"color": [143, 255, 140], "id": 256, "isthing": 0, "name": "mop"},
    {"color": [204, 255, 4], "id": 257, "isthing": 0, "name": "motorbike"},
    {"color": [255, 51, 7], "id": 258, "isthing": 0, "name": "mountain"},
    {"color": [204, 70, 3], "id": 259, "isthing": 0, "name": "mouse"},
    {"color": [0, 102, 200], "id": 260, "isthing": 0, "name": "mouse pad"},
    {"color": [61, 230, 250], "id": 261, "isthing": 0, "name": "musical instrument"},
    {"color": [255, 6, 51], "id": 262, "isthing": 0, "name": "napkin"},
    {"color": [11, 102, 255], "id": 263, "isthing": 0, "name": "net"},
    {"color": [255, 7, 71], "id": 264, "isthing": 0, "name": "newspaper"},
    {"color": [255, 9, 224], "id": 265, "isthing": 0, "name": "oar"},
    {"color": [9, 7, 230], "id": 266, "isthing": 0, "name": "ornament"},
    {"color": [220, 220, 220], "id": 267, "isthing": 0, "name": "outlet"},
    {"color": [255, 9, 92], "id": 268, "isthing": 0, "name": "oven"},
    {"color": [112, 9, 255], "id": 269, "isthing": 0, "name": "oxygen bottle"},
    {"color": [8, 255, 214], "id": 270, "isthing": 0, "name": "pack"},
    {"color": [7, 255, 224], "id": 271, "isthing": 0, "name": "pan"},
    {"color": [255, 184, 6], "id": 272, "isthing": 0, "name": "paper"},
    {"color": [10, 255, 71], "id": 273, "isthing": 0, "name": "paper box"},
    {"color": [255, 41, 10], "id": 274, "isthing": 0, "name": "paper cutter"},
    {"color": [7, 255, 255], "id": 275, "isthing": 0, "name": "parachute"},
    {"color": [224, 255, 8], "id": 276, "isthing": 0, "name": "parasol"},
    {"color": [102, 8, 255], "id": 277, "isthing": 0, "name": "parterre"},
    {"color": [255, 61, 6], "id": 278, "isthing": 0, "name": "patio"},
    {"color": [255, 194, 7], "id": 279, "isthing": 0, "name": "pelage"},
    {"color": [255, 122, 8], "id": 280, "isthing": 0, "name": "pen"},
    {"color": [0, 255, 20], "id": 281, "isthing": 0, "name": "pen container"},
    {"color": [255, 8, 41], "id": 282, "isthing": 0, "name": "pencil"},
    {"color": [255, 5, 153], "id": 283, "isthing": 0, "name": "person"},
    {"color": [6, 51, 255], "id": 284, "isthing": 0, "name": "photo"},
    {"color": [235, 12, 255], "id": 285, "isthing": 0, "name": "piano"},
    {"color": [160, 150, 20], "id": 286, "isthing": 0, "name": "picture"},
    {"color": [0, 163, 255], "id": 287, "isthing": 0, "name": "pig"},
    {"color": [140, 140, 140], "id": 288, "isthing": 0, "name": "pillar"},
    {"color": [250, 10, 15], "id": 289, "isthing": 0, "name": "pillow"},
    {"color": [20, 255, 0], "id": 290, "isthing": 0, "name": "pipe"},
    {"color": [31, 255, 0], "id": 291, "isthing": 0, "name": "pitcher"},
    {"color": [255, 31, 0], "id": 292, "isthing": 0, "name": "plant"},
    {"color": [255, 224, 0], "id": 293, "isthing": 0, "name": "plastic"},
    {"color": [153, 255, 0], "id": 294, "isthing": 0, "name": "plate"},
    {"color": [0, 0, 255], "id": 295, "isthing": 0, "name": "platform"},
    {"color": [255, 71, 0], "id": 296, "isthing": 0, "name": "player"},
    {"color": [0, 235, 255], "id": 297, "isthing": 0, "name": "playground"},
    {"color": [0, 173, 255], "id": 298, "isthing": 0, "name": "pliers"},
    {"color": [31, 0, 255], "id": 299, "isthing": 0, "name": "plume"},
    {"color": [120, 120, 120], "id": 300, "isthing": 0, "name": "poker"},
    {"color": [180, 120, 120], "id": 301, "isthing": 0, "name": "poker chip"},
    {"color": [6, 230, 230], "id": 302, "isthing": 0, "name": "pole"},
    {"color": [80, 50, 50], "id": 303, "isthing": 0, "name": "pool table"},
    {"color": [4, 200, 3], "id": 304, "isthing": 0, "name": "postcard"},
    {"color": [120, 120, 80], "id": 305, "isthing": 0, "name": "poster"},
    {"color": [140, 140, 140], "id": 306, "isthing": 0, "name": "pot"},
    {"color": [204, 5, 255], "id": 307, "isthing": 0, "name": "pottedplant"},
    {"color": [230, 230, 230], "id": 308, "isthing": 0, "name": "printer"},
    {"color": [4, 250, 7], "id": 309, "isthing": 0, "name": "projector"},
    {"color": [224, 5, 255], "id": 310, "isthing": 0, "name": "pumpkin"},
    {"color": [235, 255, 7], "id": 311, "isthing": 0, "name": "rabbit"},
    {"color": [150, 5, 61], "id": 312, "isthing": 0, "name": "racket"},
    {"color": [120, 120, 70], "id": 313, "isthing": 0, "name": "radiator"},
    {"color": [8, 255, 51], "id": 314, "isthing": 0, "name": "radio"},
    {"color": [255, 6, 82], "id": 315, "isthing": 0, "name": "rail"},
    {"color": [143, 255, 140], "id": 316, "isthing": 0, "name": "rake"},
    {"color": [204, 255, 4], "id": 317, "isthing": 0, "name": "ramp"},
    {"color": [255, 51, 7], "id": 318, "isthing": 0, "name": "range hood"},
    {"color": [204, 70, 3], "id": 319, "isthing": 0, "name": "receiver"},
    {"color": [0, 102, 200], "id": 320, "isthing": 0, "name": "recorder"},
    {"color": [61, 230, 250], "id": 321, "isthing": 0, "name": "recreational machines"},
    {"color": [255, 6, 51], "id": 322, "isthing": 0, "name": "remote control"},
    {"color": [11, 102, 255], "id": 323, "isthing": 0, "name": "road"},
    {"color": [255, 7, 71], "id": 324, "isthing": 0, "name": "robot"},
    {"color": [255, 9, 224], "id": 325, "isthing": 0, "name": "rock"},
    {"color": [9, 7, 230], "id": 326, "isthing": 0, "name": "rocket"},
    {"color": [220, 220, 220], "id": 327, "isthing": 0, "name": "rocking horse"},
    {"color": [255, 9, 92], "id": 328, "isthing": 0, "name": "rope"},
    {"color": [112, 9, 255], "id": 329, "isthing": 0, "name": "rug"},
    {"color": [8, 255, 214], "id": 330, "isthing": 0, "name": "ruler"},
    {"color": [7, 255, 224], "id": 331, "isthing": 0, "name": "runway"},
    {"color": [255, 184, 6], "id": 332, "isthing": 0, "name": "saddle"},
    {"color": [10, 255, 71], "id": 333, "isthing": 0, "name": "sand"},
    {"color": [255, 41, 10], "id": 334, "isthing": 0, "name": "saw"},
    {"color": [7, 255, 255], "id": 335, "isthing": 0, "name": "scale"},
    {"color": [224, 255, 8], "id": 336, "isthing": 0, "name": "scanner"},
    {"color": [102, 8, 255], "id": 337, "isthing": 0, "name": "scissors"},
    {"color": [255, 61, 6], "id": 338, "isthing": 0, "name": "scoop"},
    {"color": [255, 194, 7], "id": 339, "isthing": 0, "name": "screen"},
    {"color": [255, 122, 8], "id": 340, "isthing": 0, "name": "screwdriver"},
    {"color": [0, 255, 20], "id": 341, "isthing": 0, "name": "sculpture"},
    {"color": [255, 8, 41], "id": 342, "isthing": 0, "name": "scythe"},
    {"color": [255, 5, 153], "id": 343, "isthing": 0, "name": "sewer"},
    {"color": [6, 51, 255], "id": 344, "isthing": 0, "name": "sewing machine"},
    {"color": [235, 12, 255], "id": 345, "isthing": 0, "name": "shed"},
    {"color": [160, 150, 20], "id": 346, "isthing": 0, "name": "sheep"},
    {"color": [0, 163, 255], "id": 347, "isthing": 0, "name": "shell"},
    {"color": [140, 140, 140], "id": 348, "isthing": 0, "name": "shelves"},
    {"color": [250, 10, 15], "id": 349, "isthing": 0, "name": "shoe"},
    {"color": [20, 255, 0], "id": 350, "isthing": 0, "name": "shopping cart"},
    {"color": [31, 255, 0], "id": 351, "isthing": 0, "name": "shovel"},
    {"color": [255, 31, 0], "id": 352, "isthing": 0, "name": "sidecar"},
    {"color": [255, 224, 0], "id": 353, "isthing": 0, "name": "sidewalk"},
    {"color": [153, 255, 0], "id": 354, "isthing": 0, "name": "sign"},
    {"color": [0, 0, 255], "id": 355, "isthing": 0, "name": "signal light"},
    {"color": [255, 71, 0], "id": 356, "isthing": 0, "name": "sink"},
    {"color": [0, 235, 255], "id": 357, "isthing": 0, "name": "skateboard"},
    {"color": [0, 173, 255], "id": 358, "isthing": 0, "name": "ski"},
    {"color": [31, 0, 255], "id": 359, "isthing": 0, "name": "sky"},
    {"color": [120, 120, 120], "id": 360, "isthing": 0, "name": "sled"},
    {"color": [180, 120, 120], "id": 361, "isthing": 0, "name": "slippers"},
    {"color": [6, 230, 230], "id": 362, "isthing": 0, "name": "smoke"},
    {"color": [80, 50, 50], "id": 363, "isthing": 0, "name": "snail"},
    {"color": [4, 200, 3], "id": 364, "isthing": 0, "name": "snake"},
    {"color": [120, 120, 80], "id": 365, "isthing": 0, "name": "snow"},
    {"color": [140, 140, 140], "id": 366, "isthing": 0, "name": "snowmobiles"},
    {"color": [204, 5, 255], "id": 367, "isthing": 0, "name": "sofa"},
    {"color": [230, 230, 230], "id": 368, "isthing": 0, "name": "spanner"},
    {"color": [4, 250, 7], "id": 369, "isthing": 0, "name": "spatula"},
    {"color": [224, 5, 255], "id": 370, "isthing": 0, "name": "speaker"},
    {"color": [235, 255, 7], "id": 371, "isthing": 0, "name": "speed bump"},
    {"color": [150, 5, 61], "id": 372, "isthing": 0, "name": "spice container"},
    {"color": [120, 120, 70], "id": 373, "isthing": 0, "name": "spoon"},
    {"color": [8, 255, 51], "id": 374, "isthing": 0, "name": "sprayer"},
    {"color": [255, 6, 82], "id": 375, "isthing": 0, "name": "squirrel"},
    {"color": [143, 255, 140], "id": 376, "isthing": 0, "name": "stage"},
    {"color": [204, 255, 4], "id": 377, "isthing": 0, "name": "stair"},
    {"color": [255, 51, 7], "id": 378, "isthing": 0, "name": "stapler"},
    {"color": [204, 70, 3], "id": 379, "isthing": 0, "name": "stick"},
    {"color": [0, 102, 200], "id": 380, "isthing": 0, "name": "sticky note"},
    {"color": [61, 230, 250], "id": 381, "isthing": 0, "name": "stone"},
    {"color": [255, 6, 51], "id": 382, "isthing": 0, "name": "stool"},
    {"color": [11, 102, 255], "id": 383, "isthing": 0, "name": "stove"},
    {"color": [255, 7, 71], "id": 384, "isthing": 0, "name": "straw"},
    {"color": [255, 9, 224], "id": 385, "isthing": 0, "name": "stretcher"},
    {"color": [9, 7, 230], "id": 386, "isthing": 0, "name": "sun"},
    {"color": [220, 220, 220], "id": 387, "isthing": 0, "name": "sunglass"},
    {"color": [255, 9, 92], "id": 388, "isthing": 0, "name": "sunshade"},
    {"color": [112, 9, 255], "id": 389, "isthing": 0, "name": "surveillance camera"},
    {"color": [8, 255, 214], "id": 390, "isthing": 0, "name": "swan"},
    {"color": [7, 255, 224], "id": 391, "isthing": 0, "name": "sweeper"},
    {"color": [255, 184, 6], "id": 392, "isthing": 0, "name": "swim ring"},
    {"color": [10, 255, 71], "id": 393, "isthing": 0, "name": "swimming pool"},
    {"color": [255, 41, 10], "id": 394, "isthing": 0, "name": "swing"},
    {"color": [7, 255, 255], "id": 395, "isthing": 0, "name": "switch"},
    {"color": [224, 255, 8], "id": 396, "isthing": 0, "name": "table"},
    {"color": [102, 8, 255], "id": 397, "isthing": 0, "name": "tableware"},
    {"color": [255, 61, 6], "id": 398, "isthing": 0, "name": "tank"},
    {"color": [255, 194, 7], "id": 399, "isthing": 0, "name": "tap"},
    {"color": [255, 122, 8], "id": 400, "isthing": 0, "name": "tape"},
    {"color": [0, 255, 20], "id": 401, "isthing": 0, "name": "tarp"},
    {"color": [255, 8, 41], "id": 402, "isthing": 0, "name": "telephone"},
    {"color": [255, 5, 153], "id": 403, "isthing": 0, "name": "telephone booth"},
    {"color": [6, 51, 255], "id": 404, "isthing": 0, "name": "tent"},
    {"color": [235, 12, 255], "id": 405, "isthing": 0, "name": "tire"},
    {"color": [160, 150, 20], "id": 406, "isthing": 0, "name": "toaster"},
    {"color": [0, 163, 255], "id": 407, "isthing": 0, "name": "toilet"},
    {"color": [140, 140, 140], "id": 408, "isthing": 0, "name": "tong"},
    {"color": [250, 10, 15], "id": 409, "isthing": 0, "name": "tool"},
    {"color": [20, 255, 0], "id": 410, "isthing": 0, "name": "toothbrush"},
    {"color": [31, 255, 0], "id": 411, "isthing": 0, "name": "towel"},
    {"color": [255, 31, 0], "id": 412, "isthing": 0, "name": "toy"},
    {"color": [255, 224, 0], "id": 413, "isthing": 0, "name": "toy car"},
    {"color": [153, 255, 0], "id": 414, "isthing": 0, "name": "track"},
    {"color": [0, 0, 255], "id": 415, "isthing": 0, "name": "train"},
    {"color": [255, 71, 0], "id": 416, "isthing": 0, "name": "trampoline"},
    {"color": [0, 235, 255], "id": 417, "isthing": 0, "name": "trash bin"},
    {"color": [0, 173, 255], "id": 418, "isthing": 0, "name": "tray"},
    {"color": [31, 0, 255], "id": 419, "isthing": 0, "name": "tree"},
    {"color": [120, 120, 120], "id": 420, "isthing": 0, "name": "tricycle"},
    {"color": [180, 120, 120], "id": 421, "isthing": 0, "name": "tripod"},
    {"color": [6, 230, 230], "id": 422, "isthing": 0, "name": "trophy"},
    {"color": [80, 50, 50], "id": 423, "isthing": 0, "name": "truck"},
    {"color": [4, 200, 3], "id": 424, "isthing": 0, "name": "tube"},
    {"color": [120, 120, 80], "id": 425, "isthing": 0, "name": "turtle"},
    {"color": [140, 140, 140], "id": 426, "isthing": 0, "name": "tvmonitor"},
    {"color": [204, 5, 255], "id": 427, "isthing": 0, "name": "tweezers"},
    {"color": [230, 230, 230], "id": 428, "isthing": 0, "name": "typewriter"},
    {"color": [4, 250, 7], "id": 429, "isthing": 0, "name": "umbrella"},
    {"color": [224, 5, 255], "id": 430, "isthing": 0, "name": "unknown"},
    {"color": [235, 255, 7], "id": 431, "isthing": 0, "name": "vacuum cleaner"},
    {"color": [150, 5, 61], "id": 432, "isthing": 0, "name": "vending machine"},
    {"color": [120, 120, 70], "id": 433, "isthing": 0, "name": "video camera"},
    {"color": [8, 255, 51], "id": 434, "isthing": 0, "name": "video game console"},
    {"color": [255, 6, 82], "id": 435, "isthing": 0, "name": "video player"},
    {"color": [143, 255, 140], "id": 436, "isthing": 0, "name": "video tape"},
    {"color": [204, 255, 4], "id": 437, "isthing": 0, "name": "violin"},
    {"color": [255, 51, 7], "id": 438, "isthing": 0, "name": "wakeboard"},
    {"color": [204, 70, 3], "id": 439, "isthing": 0, "name": "wall"},
    {"color": [0, 102, 200], "id": 440, "isthing": 0, "name": "wallet"},
    {"color": [61, 230, 250], "id": 441, "isthing": 0, "name": "wardrobe"},
    {"color": [255, 6, 51], "id": 442, "isthing": 0, "name": "washing machine"},
    {"color": [11, 102, 255], "id": 443, "isthing": 0, "name": "watch"},
    {"color": [255, 7, 71], "id": 444, "isthing": 0, "name": "water"},
    {"color": [255, 9, 224], "id": 445, "isthing": 0, "name": "water dispenser"},
    {"color": [9, 7, 230], "id": 446, "isthing": 0, "name": "water pipe"},
    {"color": [220, 220, 220], "id": 447, "isthing": 0, "name": "water skate board"},
    {"color": [255, 9, 92], "id": 448, "isthing": 0, "name": "watermelon"},
    {"color": [112, 9, 255], "id": 449, "isthing": 0, "name": "whale"},
    {"color": [8, 255, 214], "id": 450, "isthing": 0, "name": "wharf"},
    {"color": [7, 255, 224], "id": 451, "isthing": 0, "name": "wheel"},
    {"color": [255, 184, 6], "id": 452, "isthing": 0, "name": "wheelchair"},
    {"color": [10, 255, 71], "id": 453, "isthing": 0, "name": "window"},
    {"color": [255, 41, 10], "id": 454, "isthing": 0, "name": "window blinds"},
    {"color": [7, 255, 255], "id": 455, "isthing": 0, "name": "wineglass"},
    {"color": [224, 255, 8], "id": 456, "isthing": 0, "name": "wire"},
    {"color": [102, 8, 255], "id": 457, "isthing": 0, "name": "wood"},
    {"color": [255, 61, 6], "id": 458, "isthing": 0, "name": "wool"},
]

PASCAL_VOC_21_CATEGORIES = [
    {"color": [0, 0, 0], "id": 0, "isthing": 1, "name": "background"},
    {"color": [128, 0, 0], "id": 1, "isthing": 1, "name": "aeroplane"},
    {"color": [0, 128, 0], "id": 2, "isthing": 1, "name": "bicycle"},
    {"color": [128, 128, 0], "id": 3, "isthing": 1, "name": "bird"},
    {"color": [0, 0, 128], "id": 4, "isthing": 1, "name": "boat"},
    {"color": [128, 0, 128], "id": 5, "isthing": 1, "name": "bottle"},
    {"color": [0, 128, 128], "id": 6, "isthing": 1, "name": "bus"},
    {"color": [128, 128, 128], "id": 7, "isthing": 1, "name": "car"},
    {"color": [64, 0, 0], "id": 8, "isthing": 1, "name": "cat"},
    {"color": [192, 0, 0], "id": 9, "isthing": 1, "name": "chair"},
    {"color": [64, 128, 0], "id": 10, "isthing": 1, "name": "cow"},
    {"color": [192, 128, 0], "id": 11, "isthing": 1, "name": "diningtable"},
    {"color": [64, 0, 128], "id": 12, "isthing": 1, "name": "dog"},
    {"color": [192, 0, 128], "id": 13, "isthing": 1, "name": "horse"},
    {"color": [64, 128, 128], "id": 14, "isthing": 1, "name": "motorbike"},
    {"color": [192, 128, 128], "id": 15, "isthing": 1, "name": "person"},
    {"color": [0, 64, 0], "id": 16, "isthing": 1, "name": "pottedplant"},
    {"color": [128, 64, 0], "id": 17, "isthing": 1, "name": "sheep"},
    {"color": [0, 192, 0], "id": 18, "isthing": 1, "name": "sofa"},
    {"color": [128, 192, 0], "id": 19, "isthing": 1, "name": "train"},
    {"color": [0, 64, 128], "id": 20, "isthing": 1, "name": "tvmonitor"},
]

PASCAL_PARTS_CATEGORIES = [
    {"id": 1, "name": "aeroplane body", "color": [231, 4, 237]},
    {"id": 2, "name": "aeroplane stern", "color": [116, 80, 69]},
    {"id": 3, "name": "aeroplane wing", "color": [214, 86, 123]},
    {"id": 4, "name": "aeroplane tail", "color": [22, 174, 172]},
    {"id": 5, "name": "aeroplane engine", "color": [197, 128, 182]},
    {"id": 6, "name": "aeroplane wheel", "color": [82, 197, 247]},
    {"id": 7, "name": "bicycle body", "color": [125, 34, 155]},
    {"id": 8, "name": "bicycle wheel", "color": [240, 6, 206]},
    {"id": 9, "name": "bicycle saddle", "color": [0, 67, 113]},
    {"id": 10, "name": "bicycle handlebar", "color": [112, 158, 137]},
    {"id": 11, "name": "bicycle headlight", "color": [255, 182, 87]},
    {"id": 12, "name": "bird torso", "color": [189, 249, 133]},
    {"id": 13, "name": "bird head", "color": [104, 202, 100]},
    {"id": 14, "name": "bird neck", "color": [158, 181, 70]},
    {"id": 15, "name": "bird wing", "color": [61, 245, 238]},
    {"id": 16, "name": "bird leg", "color": [55, 126, 0]},
    {"id": 17, "name": "bird foot", "color": [225, 182, 182]},
    {"id": 18, "name": "bird tail", "color": [68, 62, 33]},
    {"id": 19, "name": "boat ", "color": [200, 219, 162]},
    {"id": 20, "name": "bottle body", "color": [172, 155, 96]},
    {"id": 21, "name": "bottle cap", "color": [185, 14, 216]},
    {"id": 22, "name": "bus body", "color": [3, 58, 66]},
    {"id": 23, "name": "bus frontside", "color": [26, 173, 31]},
    {"id": 24, "name": "bus leftside", "color": [205, 197, 47]},
    {"id": 25, "name": "bus rightside", "color": [6, 223, 194]},
    {"id": 26, "name": "bus backside", "color": [10, 232, 224]},
    {"id": 27, "name": "bus roofside", "color": [189, 124, 163]},
    {"id": 28, "name": "bus mirror", "color": [253, 98, 118]},
    {"id": 29, "name": "bus fliplate", "color": [134, 124, 251]},
    {"id": 30, "name": "bus bliplate", "color": [86, 248, 252]},
    {"id": 31, "name": "bus door", "color": [104, 232, 186]},
    {"id": 32, "name": "bus wheel", "color": [73, 10, 81]},
    {"id": 33, "name": "bus headlight", "color": [83, 15, 206]},
    {"id": 34, "name": "bus window", "color": [182, 248, 35]},
    {"id": 35, "name": "car body", "color": [111, 175, 136]},
    {"id": 36, "name": "car mirror", "color": [244, 27, 39]},
    {"id": 37, "name": "car tmirror", "color": [60, 75, 197]},
    {"id": 38, "name": "car fliplate", "color": [32, 124, 177]},
    {"id": 39, "name": "car bliplate", "color": [132, 107, 137]},
    {"id": 40, "name": "car door", "color": [29, 145, 220]},
    {"id": 41, "name": "car wheel", "color": [211, 58, 216]},
    {"id": 42, "name": "car headlight", "color": [253, 195, 114]},
    {"id": 43, "name": "car window", "color": [51, 163, 166]},
    {"id": 44, "name": "cat torso", "color": [68, 44, 17]},
    {"id": 45, "name": "cat head", "color": [148, 109, 203]},
    {"id": 46, "name": "cat eye", "color": [221, 235, 212]},
    {"id": 47, "name": "cat ear", "color": [25, 226, 114]},
    {"id": 48, "name": "cat nose", "color": [99, 126, 184]},
    {"id": 49, "name": "cat neck", "color": [54, 164, 161]},
    {"id": 50, "name": "cat leg", "color": [114, 251, 219]},
    {"id": 51, "name": "cat pawn", "color": [145, 28, 176]},
    {"id": 52, "name": "cat tail", "color": [22, 29, 245]},
    {"id": 53, "name": "chair ", "color": [174, 108, 109]},
    {"id": 54, "name": "cow torso", "color": [153, 207, 125]},
    {"id": 55, "name": "cow head", "color": [243, 197, 251]},
    {"id": 56, "name": "cow eye", "color": [99, 87, 120]},
    {"id": 57, "name": "cow ear", "color": [194, 7, 114]},
    {"id": 58, "name": "cow muzzle", "color": [242, 122, 177]},
    {"id": 59, "name": "cow horn", "color": [202, 242, 232]},
    {"id": 60, "name": "cow neck", "color": [250, 136, 178]},
    {"id": 61, "name": "cow leg", "color": [171, 46, 206]},
    {"id": 62, "name": "cow tail", "color": [186, 133, 2]},
    {"id": 63, "name": "table ", "color": [201, 1, 108]},
    {"id": 64, "name": "dog torso", "color": [245, 11, 186]},
    {"id": 65, "name": "dog head", "color": [33, 191, 131]},
    {"id": 66, "name": "dog eye", "color": [225, 95, 66]},
    {"id": 67, "name": "dog ear", "color": [124, 25, 24]},
    {"id": 68, "name": "dog nose", "color": [214, 234, 112]},
    {"id": 69, "name": "dog neck", "color": [129, 83, 21]},
    {"id": 70, "name": "dog leg", "color": [185, 76, 143]},
    {"id": 71, "name": "dog pawn", "color": [180, 1, 74]},
    {"id": 72, "name": "dog tail", "color": [121, 134, 63]},
    {"id": 73, "name": "dog muzzle", "color": [90, 58, 214]},
    {"id": 74, "name": "horse body", "color": [223, 7, 152]},
    {"id": 75, "name": "horse head", "color": [154, 96, 130]},
    {"id": 76, "name": "horse eye", "color": [221, 98, 183]},
    {"id": 77, "name": "horse ear", "color": [230, 145, 183]},
    {"id": 78, "name": "horse muzzle", "color": [213, 203, 88]},
    {"id": 79, "name": "horse torso", "color": [183, 92, 254]},
    {"id": 80, "name": "horse neck", "color": [206, 114, 11]},
    {"id": 81, "name": "horse leg", "color": [214, 238, 15]},
    {"id": 82, "name": "horse tail", "color": [57, 239, 109]},
    {"id": 83, "name": "motorbike body", "color": [197, 138, 146]},
    {"id": 84, "name": "motorbike wheel", "color": [124, 107, 252]},
    {"id": 85, "name": "motorbike handlebar", "color": [163, 225, 169]},
    {"id": 86, "name": "motorbike saddle", "color": [254, 180, 116]},
    {"id": 87, "name": "motorbike headlight", "color": [119, 52, 22]},
    {"id": 88, "name": "person body", "color": [198, 68, 18]},
    {"id": 89, "name": "person head", "color": [40, 30, 77]},
    {"id": 90, "name": "person eye", "color": [237, 64, 148]},
    {"id": 91, "name": "person ear", "color": [49, 186, 234]},
    {"id": 92, "name": "person ebrow", "color": [242, 204, 127]},
    {"id": 93, "name": "person nose", "color": [101, 145, 176]},
    {"id": 94, "name": "person mouth", "color": [31, 78, 216]},
    {"id": 95, "name": "person hair", "color": [95, 148, 151]},
    {"id": 96, "name": "person torso", "color": [126, 117, 235]},
    {"id": 97, "name": "person neck", "color": [13, 146, 62]},
    {"id": 98, "name": "person lower arm", "color": [9, 41, 5]},
    {"id": 99, "name": "person upper arm", "color": [110, 109, 109]},
    {"id": 100, "name": "person hand", "color": [58, 227, 163]},
    {"id": 101, "name": "person lower leg", "color": [132, 63, 32]},
    {"id": 102, "name": "person upper leg", "color": [212, 118, 174]},
    {"id": 103, "name": "person foot", "color": [45, 66, 254]},
    {"id": 104, "name": "pottedplant plant", "color": [236, 149, 209]},
    {"id": 105, "name": "pottedplant pot", "color": [80, 197, 134]},
    {"id": 106, "name": "sheep torso", "color": [241, 111, 194]},
    {"id": 107, "name": "sheep head", "color": [31, 13, 13]},
    {"id": 108, "name": "sheep eye", "color": [34, 207, 63]},
    {"id": 109, "name": "sheep ear", "color": [249, 117, 121]},
    {"id": 110, "name": "sheep muzzle", "color": [172, 128, 70]},
    {"id": 111, "name": "sheep horn", "color": [97, 144, 104]},
    {"id": 112, "name": "sheep neck", "color": [121, 163, 14]},
    {"id": 113, "name": "sheep leg", "color": [38, 79, 231]},
    {"id": 114, "name": "sheep tail", "color": [218, 195, 52]},
    {"id": 115, "name": "sofa ", "color": [102, 8, 225]},
    {"id": 116, "name": "train body", "color": [150, 44, 180]},
    {"id": 117, "name": "train head", "color": [99, 250, 180]},
    {"id": 118, "name": "train headlight", "color": [24, 148, 249]},
    {"id": 119, "name": "train coach", "color": [143, 232, 181]},
    {"id": 120, "name": "tvmonitor frame", "color": [68, 191, 134]},
    {"id": 121, "name": "tvmonitor screen", "color": [186, 6, 38]},
    {"id": 122, "name": "bag ", "color": [215, 253, 9]},
    {"id": 123, "name": "basket ", "color": [150, 44, 154]},
    {"id": 124, "name": "bed ", "color": [66, 132, 108]},
    {"id": 125, "name": "bedclothes ", "color": [193, 84, 92]},
    {"id": 126, "name": "bench ", "color": [84, 154, 254]},
    {"id": 127, "name": "bird cage ", "color": [2, 93, 169]},
    {"id": 128, "name": "board ", "color": [41, 254, 95]},
    {"id": 129, "name": "book ", "color": [157, 228, 148]},
    {"id": 130, "name": "bowl ", "color": [201, 198, 2]},
    {"id": 131, "name": "box ", "color": [237, 151, 223]},
    {"id": 132, "name": "bridge ", "color": [74, 200, 197]},
    {"id": 133, "name": "brush ", "color": [157, 2, 192]},
    {"id": 134, "name": "bucket ", "color": [62, 8, 145]},
    {"id": 135, "name": "building ", "color": [244, 158, 23]},
    {"id": 136, "name": "cabinet ", "color": [143, 34, 160]},
    {"id": 137, "name": "cage ", "color": [74, 182, 153]},
    {"id": 138, "name": "case ", "color": [44, 161, 32]},
    {"id": 139, "name": "ceiling ", "color": [22, 207, 172]},
    {"id": 140, "name": "cloth ", "color": [62, 233, 51]},
    {"id": 141, "name": "computer ", "color": [203, 221, 8]},
    {"id": 142, "name": "counter ", "color": [155, 154, 208]},
    {"id": 143, "name": "cup ", "color": [136, 170, 161]},
    {"id": 144, "name": "curtain ", "color": [69, 238, 67]},
    {"id": 145, "name": "cushion ", "color": [250, 140, 63]},
    {"id": 146, "name": "door ", "color": [228, 29, 142]},
    {"id": 147, "name": "fence ", "color": [149, 149, 255]},
    {"id": 148, "name": "fire place ", "color": [25, 17, 14]},
    {"id": 149, "name": "floor ", "color": [141, 121, 107]},
    {"id": 150, "name": "flower ", "color": [196, 171, 99]},
    {"id": 151, "name": "food ", "color": [246, 30, 195]},
    {"id": 152, "name": "fridge ", "color": [95, 2, 42]},
    {"id": 153, "name": "grandstand ", "color": [174, 116, 162]},
    {"id": 154, "name": "grass ", "color": [251, 58, 246]},
    {"id": 155, "name": "ground ", "color": [138, 68, 168]},
    {"id": 156, "name": "horse-drawn carriage ", "color": [236, 220, 194]},
    {"id": 157, "name": "keyboard ", "color": [228, 180, 129]},
    {"id": 158, "name": "laptop ", "color": [41, 39, 187]},
    {"id": 159, "name": "light ", "color": [18, 155, 71]},
    {"id": 160, "name": "mat ", "color": [81, 149, 168]},
    {"id": 161, "name": "metal ", "color": [222, 250, 122]},
    {"id": 162, "name": "mirror ", "color": [27, 14, 162]},
    {"id": 163, "name": "mountain ", "color": [96, 67, 42]},
    {"id": 164, "name": "mouse ", "color": [248, 27, 142]},
    {"id": 165, "name": "pack ", "color": [48, 208, 79]},
    {"id": 166, "name": "paper ", "color": [85, 44, 114]},
    {"id": 167, "name": "picture ", "color": [8, 66, 36]},
    {"id": 168, "name": "pillow ", "color": [199, 38, 36]},
    {"id": 169, "name": "plant ", "color": [45, 67, 214]},
    {"id": 170, "name": "plate ", "color": [176, 85, 199]},
    {"id": 171, "name": "platform ", "color": [118, 46, 134]},
    {"id": 172, "name": "pole ", "color": [66, 53, 97]},
    {"id": 173, "name": "poster ", "color": [134, 95, 198]},
    {"id": 174, "name": "pot ", "color": [56, 185, 27]},
    {"id": 175, "name": "road ", "color": [12, 12, 242]},
    {"id": 176, "name": "rock ", "color": [141, 182, 239]},
    {"id": 177, "name": "rope ", "color": [242, 15, 134]},
    {"id": 178, "name": "rug ", "color": [119, 78, 116]},
    {"id": 179, "name": "sand ", "color": [159, 25, 177]},
    {"id": 180, "name": "sculpture ", "color": [155, 71, 2]},
    {"id": 181, "name": "shelves ", "color": [13, 156, 172]},
    {"id": 182, "name": "sidewalk ", "color": [153, 56, 74]},
    {"id": 183, "name": "sign ", "color": [132, 5, 169]},
    {"id": 184, "name": "sink ", "color": [202, 115, 244]},
    {"id": 185, "name": "sky ", "color": [189, 81, 126]},
    {"id": 186, "name": "smoke ", "color": [50, 105, 141]},
    {"id": 187, "name": "snow ", "color": [163, 75, 126]},
    {"id": 188, "name": "speaker ", "color": [25, 28, 9]},
    {"id": 189, "name": "stage ", "color": [57, 175, 211]},
    {"id": 190, "name": "stair ", "color": [36, 182, 123]},
    {"id": 191, "name": "tent ", "color": [210, 184, 159]},
    {"id": 192, "name": "toy ", "color": [139, 14, 196]},
    {"id": 193, "name": "track ", "color": [204, 225, 55]},
    {"id": 194, "name": "tree ", "color": [145, 64, 92]},
    {"id": 195, "name": "truck ", "color": [43, 65, 241]},
    {"id": 196, "name": "wall ", "color": [220, 189, 61]},
    {"id": 197, "name": "water ", "color": [250, 95, 220]},
    {"id": 198, "name": "window ", "color": [176, 117, 245]},
    {"id": 199, "name": "wineglass ", "color": [102, 162, 66]},
    {"id": 200, "name": "wood ", "color": [100, 60, 45]},
]

PASCAL_PARTS_PARTS_ONLY = [
    {"id": 1, "name": "aeroplane body", "color": [30, 178, 112]},
    {"id": 2, "name": "aeroplane stern", "color": [0, 80, 42]},
    {"id": 3, "name": "aeroplane wing", "color": [160, 237, 245]},
    {"id": 4, "name": "aeroplane engine", "color": [144, 222, 51]},
    {"id": 5, "name": "aeroplane wheel", "color": [155, 121, 20]},
    {"id": 6, "name": "bicycle body", "color": [24, 50, 96]},
    {"id": 7, "name": "bicycle wheel", "color": [247, 201, 171]},
    {"id": 8, "name": "bird torso", "color": [63, 162, 50]},
    {"id": 9, "name": "bird head", "color": [143, 18, 27]},
    {"id": 10, "name": "bird wing", "color": [204, 34, 128]},
    {"id": 11, "name": "bird leg", "color": [31, 37, 39]},
    {"id": 12, "name": "boat ", "color": [175, 16, 226]},
    {"id": 13, "name": "bottle cap", "color": [31, 13, 221]},
    {"id": 14, "name": "bottle body", "color": [120, 248, 33]},
    {"id": 15, "name": "bus body", "color": [243, 104, 244]},
    {"id": 16, "name": "bus wheel", "color": [247, 196, 104]},
    {"id": 17, "name": "bus window", "color": [63, 138, 111]},
    {"id": 18, "name": "car body", "color": [200, 176, 116]},
    {"id": 19, "name": "car license plate", "color": [79, 146, 205]},
    {"id": 20, "name": "car wheel", "color": [231, 126, 229]},
    {"id": 21, "name": "car light", "color": [120, 219, 85]},
    {"id": 22, "name": "car window", "color": [240, 73, 236]},
    {"id": 23, "name": "cat torso", "color": [24, 254, 246]},
    {"id": 24, "name": "cat head", "color": [38, 29, 151]},
    {"id": 25, "name": "cat leg", "color": [229, 8, 161]},
    {"id": 26, "name": "cat tail", "color": [212, 191, 142]},
    {"id": 27, "name": "chair ", "color": [235, 90, 210]},
    {"id": 28, "name": "cow torso", "color": [72, 26, 132]},
    {"id": 29, "name": "cow head", "color": [28, 249, 68]},
    {"id": 30, "name": "cow leg", "color": [69, 62, 39]},
    {"id": 31, "name": "cow tail", "color": [238, 140, 59]},
    {"id": 32, "name": "table ", "color": [73, 170, 102]},
    {"id": 33, "name": "dog torso", "color": [51, 140, 200]},
    {"id": 34, "name": "dog head", "color": [141, 130, 240]},
    {"id": 35, "name": "dog leg", "color": [223, 199, 36]},
    {"id": 36, "name": "dog tail", "color": [40, 192, 182]},
    {"id": 37, "name": "horse torso", "color": [212, 206, 245]},
    {"id": 38, "name": "horse head", "color": [59, 63, 103]},
    {"id": 39, "name": "horse leg", "color": [50, 72, 178]},
    {"id": 40, "name": "horse tail", "color": [49, 64, 103]},
    {"id": 41, "name": "motorbike body", "color": [226, 39, 217]},
    {"id": 42, "name": "motorbike wheel", "color": [11, 110, 195]},
    {"id": 43, "name": "person torso", "color": [155, 219, 139]},
    {"id": 44, "name": "person head", "color": [168, 137, 15]},
    {"id": 45, "name": "person lower arm", "color": [187, 194, 167]},
    {"id": 46, "name": "person upper arm", "color": [60, 80, 21]},
    {"id": 47, "name": "person lower leg", "color": [180, 219, 17]},
    {"id": 48, "name": "person upper leg", "color": [240, 249, 227]},
    {"id": 49, "name": "pottedplant plant", "color": [191, 176, 151]},
    {"id": 50, "name": "pottedplant pot", "color": [13, 133, 225]},
    {"id": 51, "name": "sheep torso", "color": [178, 101, 246]},
    {"id": 52, "name": "sheep head", "color": [52, 108, 42]},
    {"id": 53, "name": "sheep leg", "color": [92, 169, 47]},
    {"id": 54, "name": "sofa ", "color": [45, 45, 192]},
    {"id": 55, "name": "train body", "color": [168, 7, 178]},
    {"id": 56, "name": "tvmonitor frame", "color": [59, 89, 2]},
    {"id": 57, "name": "tvmonitor screen", "color": [51, 85, 167]},
]


def _get_ctx59_meta():
    # Id 0 is reserved for ignore_label, we change ignore_label for 0
    # to 255 in our pre-processing, so all ids are shifted by 1.
    stuff_ids = [k["id"] for k in PASCAL_CTX_59_CATEGORIES]
    assert len(stuff_ids) == 59, len(stuff_ids)

    # For semantic segmentation, this mapping maps from contiguous stuff id
    # (in [0, 91], used in models) to ids in the dataset (used for processing results)
    stuff_dataset_id_to_contiguous_id = {k: i for i, k in enumerate(stuff_ids)}
    stuff_classes = [k["name"] for k in PASCAL_CTX_59_CATEGORIES]

    ret = {
        "stuff_dataset_id_to_contiguous_id": stuff_dataset_id_to_contiguous_id,
        "stuff_classes": stuff_classes,
    }
    return ret


def register_all_ctx59(root):
    root = os.path.join(root, "pascal_ctx_d2")
    meta = _get_ctx59_meta()
    for name, dirname in [("train", "training"), ("val", "validation")]:
        image_dir = os.path.join(root, "images", dirname)
        gt_dir = os.path.join(root, "annotations_ctx59", dirname)
        name = f"ctx59_sem_seg_{name}"
        DatasetCatalog.register(
            name,
            lambda x=image_dir, y=gt_dir: load_sem_seg(
                y, x, gt_ext="png", image_ext="jpg", dataset_name="pascal_context_59"
            ),
        )
        MetadataCatalog.get(name).set(
            stuff_classes=meta["stuff_classes"][:],
            thing_dataset_id_to_contiguous_id={},  # to make Mask2Former happy
            stuff_dataset_id_to_contiguous_id=meta["stuff_dataset_id_to_contiguous_id"],
            image_root=image_dir,
            sem_seg_root=gt_dir,
            evaluator_type="sem_seg",
            ignore_label=255,
        )


def _get_pascal21_meta():
    # Id 0 is reserved for ignore_label, we change ignore_label for 0
    # to 255 in our pre-processing, so all ids are shifted by 1.
    stuff_ids = [k["id"] for k in PASCAL_VOC_21_CATEGORIES]
    assert len(stuff_ids) == 21, len(stuff_ids)

    # For semantic segmentation, this mapping maps from contiguous stuff id
    # (in [0, 91], used in models) to ids in the dataset (used for processing results)
    stuff_dataset_id_to_contiguous_id = {k: i for i, k in enumerate(stuff_ids)}
    stuff_classes = [k["name"] for k in PASCAL_VOC_21_CATEGORIES]

    ret = {
        "stuff_dataset_id_to_contiguous_id": stuff_dataset_id_to_contiguous_id,
        "stuff_classes": stuff_classes,
    }
    return ret


def register_all_pascal21(root):
    root = os.path.join(root, "pascal_voc_d2")
    meta = _get_pascal21_meta()
    for name, dirname in [("train", "training"), ("val", "validation")]:
        image_dir = os.path.join(root, "images", dirname)
        gt_dir = os.path.join(root, "annotations_pascal21", dirname)
        name = f"pascal21_sem_seg_{name}"
        DatasetCatalog.register(
            name,
            lambda x=image_dir, y=gt_dir: load_sem_seg(
                y, x, gt_ext="png", image_ext="jpg", dataset_name="pascal_voc_21"
            ),
        )
        MetadataCatalog.get(name).set(
            stuff_classes=meta["stuff_classes"][:],
            thing_dataset_id_to_contiguous_id={},  # to make Mask2Former happy
            stuff_dataset_id_to_contiguous_id=meta["stuff_dataset_id_to_contiguous_id"],
            image_root=image_dir,
            sem_seg_root=gt_dir,
            evaluator_type="sem_seg",
            ignore_label=255,
        )


def _get_ctx459_meta():
    # Id 0 is reserved for ignore_label, we change ignore_label for 0
    # to 255 in our pre-processing, so all ids are shifted by 1.
    stuff_ids = [k["id"] for k in PASCAL_CTX_459_CATEGORIES]
    assert len(stuff_ids) == 459, len(stuff_ids)

    # For semantic segmentation, this mapping maps from contiguous stuff id
    # (in [0, 91], used in models) to ids in the dataset (used for processing results)
    stuff_dataset_id_to_contiguous_id = {k: i for i, k in enumerate(stuff_ids)}
    stuff_classes = [k["name"] for k in PASCAL_CTX_459_CATEGORIES]

    ret = {
        "stuff_dataset_id_to_contiguous_id": stuff_dataset_id_to_contiguous_id,
        "stuff_classes": stuff_classes,
    }
    return ret


def register_all_ctx459(root):
    root = os.path.join(root, "pascal_ctx_d2")
    meta = _get_ctx459_meta()
    for name, dirname in [("train", "training"), ("val", "validation")]:
        image_dir = os.path.join(root, "images", dirname)
        gt_dir = os.path.join(root, "annotations_ctx459", dirname)
        name = f"ctx459_sem_seg_{name}"
        DatasetCatalog.register(
            name,
            lambda x=image_dir, y=gt_dir: load_sem_seg(
                y, x, gt_ext="tif", image_ext="jpg", dataset_name="pascal_context_459"
            ),
        )
        MetadataCatalog.get(name).set(
            stuff_classes=meta["stuff_classes"][:],
            thing_dataset_id_to_contiguous_id={},  # to make Mask2Former happy
            stuff_dataset_id_to_contiguous_id=meta["stuff_dataset_id_to_contiguous_id"],
            image_root=image_dir,
            sem_seg_root=gt_dir,
            evaluator_type="sem_seg",
            ignore_label=65535,  # NOTE: gt is saved in 16-bit TIFF images
        )


def _get_parts_meta():
    # Id 0 is reserved for ignore_label, we change ignore_label for 0
    # to 255 in our pre-processing, so all ids are shifted by 1.
    stuff_ids = [k["id"] for k in PASCAL_PARTS_CATEGORIES]
    # assert len(stuff_ids) == 459, len(stuff_ids)

    # For semantic segmentation, this mapping maps from contiguous stuff id
    # (in [0, 91], used in models) to ids in the dataset (used for processing results)
    stuff_dataset_id_to_contiguous_id = {k: i for i, k in enumerate(stuff_ids)}
    stuff_classes = [k["name"] for k in PASCAL_PARTS_CATEGORIES]

    ret = {
        "stuff_dataset_id_to_contiguous_id": stuff_dataset_id_to_contiguous_id,
        "stuff_classes": stuff_classes,
    }
    return ret


def _get_parts_only_meta():
    # Id 0 is reserved for ignore_label, we change ignore_label for 0
    # to 255 in our pre-processing, so all ids are shifted by 1.
    stuff_ids = [k["id"] for k in PASCAL_PARTS_PARTS_ONLY]
    # assert len(stuff_ids) == 459, len(stuff_ids)

    # For semantic segmentation, this mapping maps from contiguous stuff id
    # (in [0, 91], used in models) to ids in the dataset (used for processing results)
    stuff_dataset_id_to_contiguous_id = {k: i for i, k in enumerate(stuff_ids)}
    stuff_classes = [k["name"] for k in PASCAL_PARTS_PARTS_ONLY]

    ret = {
        "stuff_dataset_id_to_contiguous_id": stuff_dataset_id_to_contiguous_id,
        "stuff_classes": stuff_classes,
    }
    return ret


def register_all_pascal_parts_only(root):
    data_root = root
    root = os.path.join(root, "pascal_parts")
    meta = _get_parts_only_meta()
    for name, dirname in [
        ("train", "training_merged"),
        ("val", "validation_merged"),
        ("test", "test_merged"),
    ]:
        image_dir = os.path.join(data_root, "VOCdevkit/VOC2010/JPEGImages")
        gt_dir = os.path.join(root, "labels", dirname)
        name = f"pascal_parts_merged_{name}"
        DatasetCatalog.register(
            name,
            lambda x=image_dir, y=gt_dir: load_sem_seg(
                y, x, gt_ext="tif", image_ext="jpg", dataset_name="pascal_parts_merged"
            ),
        )
        MetadataCatalog.get(name).set(
            stuff_classes=meta["stuff_classes"][:],
            thing_dataset_id_to_contiguous_id={},  # to make Mask2Former happy
            stuff_dataset_id_to_contiguous_id=meta["stuff_dataset_id_to_contiguous_id"],
            image_root=image_dir,
            sem_seg_root=gt_dir,
            evaluator_type="sem_seg",
            ignore_label=0,  # NOTE: gt is saved in 16-bit TIFF images
        )


PASCAL_LABEL_PART_GROUP = {
    1: 1,
    2: 2,
    3: 3,
    4: 2,
    5: 4,
    6: 5,
    7: 6,
    8: 7,
    9: 6,
    10: 6,
    11: 6,
    12: 8,
    13: 9,
    14: 9,
    15: 10,
    16: 11,
    17: 11,
    18: 8,
    19: 12,
    20: 14,
    21: 13,
    22: 15,
    23: 15,
    24: 15,
    25: 15,
    26: 15,
    27: 15,
    28: 15,
    29: 15,
    30: 15,
    31: 15,
    32: 16,
    33: 15,
    34: 17,
    35: 18,
    36: 18,
    37: 18,
    38: 19,
    39: 19,
    40: 18,
    41: 20,
    42: 21,
    43: 22,
    44: 23,
    45: 24,
    46: 24,
    47: 24,
    48: 24,
    49: 23,
    50: 25,
    51: 25,
    52: 26,
    53: 27,
    54: 28,
    55: 29,
    56: 29,
    57: 29,
    58: 29,
    59: 29,
    60: 28,
    61: 30,
    62: 31,
    63: 32,
    64: 33,
    65: 34,
    66: 34,
    67: 34,
    68: 34,
    69: 33,
    70: 35,
    71: 35,
    72: 36,
    73: 34,
    74: 37,
    75: 38,
    76: 38,
    77: 38,
    78: 38,
    79: 37,
    80: 37,
    81: 39,
    82: 40,
    83: 41,
    84: 42,
    85: 41,
    86: 41,
    87: 41,
    88: 43,
    89: 44,
    90: 44,
    91: 44,
    92: 44,
    93: 44,
    94: 44,
    95: 44,
    96: 43,
    97: 43,
    98: 45,
    99: 46,
    100: 45,
    101: 47,
    102: 48,
    103: 47,
    104: 49,
    105: 50,
    106: 51,
    107: 52,
    108: 52,
    109: 52,
    110: 52,
    111: 52,
    112: 51,
    113: 53,
    114: 51,
    115: 54,
    116: 55,
    117: 55,
    118: 55,
    119: 55,
    120: 56,
    121: 57,
}


def register_all_pascal_parts(root):
    data_root = root
    register_all_pascal_parts_only(data_root)
    root = os.path.join(root, "pascal_parts")
    meta = _get_parts_meta()
    for name, dirname in [("train", "training"), ("val", "validation"), ("test", "test_pano")]:
        image_dir = os.path.join(data_root, "VOCdevkit/VOC2010/JPEGImages")
        gt_dir = os.path.join(root, "labels", dirname)
        name = f"pascal_parts_{name}"
        DatasetCatalog.register(
            name, lambda x=image_dir, y=gt_dir: load_sem_seg(y, x, gt_ext="tif", image_ext="jpg")
        )
        MetadataCatalog.get(name).set(
            stuff_classes=meta["stuff_classes"][:],
            thing_dataset_id_to_contiguous_id={},  # to make Mask2Former happy
            stuff_dataset_id_to_contiguous_id=meta["stuff_dataset_id_to_contiguous_id"],
            image_root=image_dir,
            sem_seg_root=gt_dir,
            evaluator_type="sem_seg",
            label_group=PASCAL_LABEL_PART_GROUP,
            # ignore_label=0,  # NOTE: gt is saved in 16-bit TIFF images
            ignore_label=255,  # NOTE: gt is saved in 16-bit TIFF images
        )


_PREDEFINED_SPLITS_PASCALVOCPART = {}
_PREDEFINED_SPLITS_PASCALVOCPART["pascalvocpart"] = {
    "pascalvocpart": (
        "VOCdevkit/VOC2010/JPEGImages",
        "pascal_parts/pascalvocpart_training_instance.json",
    ),
    "pascalvocpart_train": (
        "VOCdevkit/VOC2010/JPEGImages",
        "pascal_parts/pascalvocpart_training_instance.json",
    ),
    "pascalvocpart_val": (
        "VOCdevkit/VOC2010/JPEGImages",
        "pascal_parts/pascalvocpart_validation_instance.json",
    ),
}


def _get_builtin_metadata(dataset_name):
    return _get_pascalvocpart_metadata([])

    raise KeyError("No built-in metadata for dataset {}".format(dataset_name))


def _get_pascalvocpart_metadata(categories):
    if len(categories) == 0:
        return {}
    id_to_name = {x["id"]: x["name"] for x in categories}
    thing_dataset_id_to_contiguous_id = {i: i for i in range(len(categories))}
    thing_classes = [id_to_name[k] for k in sorted(id_to_name)]
    return {
        "thing_dataset_id_to_contiguous_id": thing_dataset_id_to_contiguous_id,
        "thing_classes": thing_classes,
    }


def register_all_pascalvocpart(root):
    for dataset_name, splits_per_dataset in _PREDEFINED_SPLITS_PASCALVOCPART.items():
        for key, (image_root, json_file) in splits_per_dataset.items():
            custom_register_coco_instances(
                key,
                _get_builtin_metadata(dataset_name),
                os.path.join(root, json_file) if "://" not in json_file else json_file,
                os.path.join(root, image_root),
            )


# register_all_ctx59(os.getenv("DETECTRON2_DATASETS", "datasets"))
# register_all_pascal21(os.getenv("DETECTRON2_DATASETS", "datasets"))
# register_all_ctx459(os.getenv("DETECTRON2_DATASETS", "datasets"))

# True for open source;
# Internally at fb, we register them elsewhere
if __name__.endswith(".pascal_voc_external"):
    # Assume pre-defined datasets live in `./datasets`.
    _root = os.path.expanduser(os.getenv("DETECTRON2_DATASETS", "datasets"))
    # register_all_pascal_parts(_root)
    register_all_pascalvocpart(_root)
