import copy
import logging
import math
from typing import Dict, List, Optional, Tuple

import torch
import torch.nn as nn
import torch.nn.functional as F

from ape.layers import VisionLanguageAlign, ZeroShotFC
from detectron2.layers import move_device_like
from detectron2.modeling import GeneralizedRCNN, detector_postprocess
from detectron2.modeling.roi_heads.fast_rcnn import fast_rcnn_inference
from detectron2.structures import Boxes, ImageList, Instances
from detrex.layers import MLP, box_cxcywh_to_xyxy, box_xyxy_to_cxcywh
from detrex.utils import inverse_sigmoid
from torchvision.ops.boxes import batched_nms

logger = logging.getLogger(__name__)


class DeformableDETR(nn.Module):
    """Implements the Deformable DETR model.

    Code is modified from the `official github repo
    <https://github.com/fundamentalvision/Deformable-DETR>`_.

    More details can be found in the `paper
    <https://arxiv.org/abs/2010.04159>`_ .

    Args:
        backbone (nn.Module): the backbone module.
        position_embedding (nn.Module): the position embedding module.
        neck (nn.Module): the neck module.
        transformer (nn.Module): the transformer module.
        embed_dim (int): the dimension of the embedding.
        num_classes (int): Number of total categories.
        num_queries (int): Number of proposal dynamic anchor boxes in Transformer
        criterion (nn.Module): Criterion for calculating the total losses.
        pixel_mean (List[float]): Pixel mean value for image normalization.
            Default: [123.675, 116.280, 103.530].
        pixel_std (List[float]): Pixel std value for image normalization.
            Default: [58.395, 57.120, 57.375].
        aux_loss (bool): whether to use auxiliary loss. Default: True.
        with_box_refine (bool): whether to use box refinement. Default: False.
        as_two_stage (bool): whether to use two-stage. Default: False.
        select_box_nums_for_evaluation (int): the number of topk candidates
            slected at postprocess for evaluation. Default: 100.

    """

    def __init__(
        self,
        backbone,
        position_embedding,
        neck,
        transformer,
        embed_dim,
        num_classes,
        num_queries,
        criterion,
        pixel_mean: Tuple[float],
        pixel_std: Tuple[float],
        aux_loss=True,
        with_box_refine=False,
        as_two_stage=False,
        select_box_nums_for_evaluation=100,
        select_box_nums_for_evaluation_list: list = None,
        input_format: Optional[str] = None,
        vis_period: int = 0,
        output_dir: Optional[str] = None,
        dataset_names: List[str] = [],
        dataset_metas: List[str] = [],
        dataset_prompts: List[str] = None,
        embed_dim_language: int = 512,
        text_feature_batch_repeat: bool = True,
        text_feature_bank: bool = False,
        text_feature_bank_reset: bool = False,
        text_feature_bank_random_size: bool = False,
        text_feature_reduce_type: str = "last",
        text_feature_reduce_before_fusion: bool = True,
        expression_cumulative_gt_class: bool = True,
        test_nms_thresh: float = 0.7,
        test_score_thresh: float = 0.0,
        last_class_embed_use_mlp: bool = False,
        openset_classifier: str = "VisionLanguageAlign",
    ):
        super().__init__()
        self.backbone = backbone
        self.position_embedding = position_embedding

        self.neck = neck

        self.num_queries = num_queries
        if not as_two_stage:
            self.query_embedding = nn.Embedding(num_queries, embed_dim * 2)

        self.transformer = transformer

        self.num_classes = num_classes
        if criterion[0].loss_class_type == "ce_loss":
            self.class_embed = nn.Linear(embed_dim, num_classes + 1)
        else:
            self.class_embed = nn.Linear(embed_dim, num_classes)
        self.bbox_embed = MLP(embed_dim, embed_dim, 4, 3)

        self.aux_loss = aux_loss
        self.criterion = nn.ModuleList(criterion)

        self.with_box_refine = with_box_refine
        self.as_two_stage = as_two_stage

        prior_prob = 0.01
        bias_value = -math.log((1 - prior_prob) / prior_prob)
        if criterion[0].loss_class_type == "ce_loss":
            self.class_embed.bias.data = torch.ones(num_classes + 1) * bias_value
        else:
            self.class_embed.bias.data = torch.ones(num_classes) * bias_value
        nn.init.constant_(self.bbox_embed.layers[-1].weight.data, 0)
        nn.init.constant_(self.bbox_embed.layers[-1].bias.data, 0)
        if self.neck is not None:
            for _, neck_layer in self.neck.named_modules():
                if isinstance(neck_layer, nn.Conv2d):
                    nn.init.xavier_uniform_(neck_layer.weight, gain=1)
                    nn.init.constant_(neck_layer.bias, 0)

        self.text_feature_batch_repeat = text_feature_batch_repeat
        if openset_classifier == "ZeroShotFC":
            del self.class_embed
            self.class_embed = ZeroShotFC(
                input_size=embed_dim,
                num_classes=num_classes,
                zs_weight_path="zeros",
                zs_weight_dim=embed_dim_language,
                use_bias=0.0,
                norm_weight=True,
                norm_temperature=50.0,
                use_project=True,
                use_sigmoid_ce=True,
                prior_prob=0.01,
                zs_vocabulary="",
                text_model="",
            )

        if openset_classifier == "VisionLanguageAlign":
            del self.class_embed
            self.class_embed = VisionLanguageAlign(embed_dim, embed_dim_language)

        num_pred = (
            (transformer.decoder.num_layers + 1) if as_two_stage else transformer.decoder.num_layers
        )
        if with_box_refine:
            self.class_embed = nn.ModuleList(
                [copy.deepcopy(self.class_embed) for i in range(num_pred)]
            )
            self.bbox_embed = nn.ModuleList(
                [copy.deepcopy(self.bbox_embed) for i in range(num_pred)]
            )
            nn.init.constant_(self.bbox_embed[0].layers[-1].bias.data[2:], -2.0)
            self.transformer.decoder.bbox_embed = self.bbox_embed
        else:
            nn.init.constant_(self.bbox_embed.layers[-1].bias.data[2:], -2.0)
            self.class_embed = nn.ModuleList([self.class_embed for _ in range(num_pred)])
            self.bbox_embed = nn.ModuleList([self.bbox_embed for _ in range(num_pred)])
            self.transformer.decoder.bbox_embed = None

        if as_two_stage:
            self.transformer.decoder.class_embed = self.class_embed
            if True:
                prior_prob = 0.01
                bias_value = -math.log((1 - prior_prob) / prior_prob)
                if criterion[0].loss_class_type == "ce_loss":
                    self.transformer.decoder.class_embed[-1] = nn.Linear(embed_dim, num_classes + 1)
                    self.transformer.decoder.class_embed[-1].bias.data = (
                        torch.ones(num_classes + 1) * bias_value
                    )
                else:
                    self.transformer.decoder.class_embed[-1] = nn.Linear(embed_dim, 1)
                    self.transformer.decoder.class_embed[-1].bias.data = torch.ones(1) * bias_value
                    if last_class_embed_use_mlp:
                        self.transformer.decoder.class_embed[-1] = MLP(embed_dim, embed_dim, 1, 3)
                        self.transformer.decoder.class_embed[-1].layers[-1].bias.data = (
                            torch.ones(1) * bias_value
                        )
            for box_embed in self.bbox_embed:
                nn.init.constant_(box_embed.layers[-1].bias.data[2:], 0.0)

            if self.transformer.proposal_ambiguous:
                self.transformer.decoder.bbox_embed_ambiguous = nn.ModuleList(
                    [
                        copy.deepcopy(self.transformer.decoder.bbox_embed[-1])
                        for _ in range(self.transformer.proposal_ambiguous)
                    ]
                )
                self.transformer.decoder.class_embed_ambiguous = nn.ModuleList(
                    [
                        copy.deepcopy(self.transformer.decoder.class_embed[-1])
                        for _ in range(self.transformer.proposal_ambiguous)
                    ]
                )

            if False:
                self.transformer.decoder.bbox_embed_2 = copy.deepcopy(
                    self.transformer.decoder.bbox_embed[-1]
                )
                self.transformer.decoder.class_embed_2 = copy.deepcopy(
                    self.transformer.decoder.class_embed[-1]
                )

                self.transformer.decoder.bbox_embed_3 = copy.deepcopy(
                    self.transformer.decoder.bbox_embed[-1]
                )
                self.transformer.decoder.class_embed_3 = copy.deepcopy(
                    self.transformer.decoder.class_embed[-1]
                )

        self.select_box_nums_for_evaluation = select_box_nums_for_evaluation
        self.select_box_nums_for_evaluation_list = select_box_nums_for_evaluation_list

        self.test_topk_per_image = self.select_box_nums_for_evaluation
        self.test_nms_thresh = test_nms_thresh
        self.test_score_thresh = test_score_thresh

        self.input_format = input_format
        self.vis_period = vis_period
        if vis_period > 0:
            assert input_format is not None, "input_format is required for visualization!"

        self.register_buffer("pixel_mean", torch.tensor(pixel_mean).view(-1, 1, 1), False)
        self.register_buffer("pixel_std", torch.tensor(pixel_std).view(-1, 1, 1), False)
        assert (
            self.pixel_mean.shape == self.pixel_std.shape
        ), f"{self.pixel_mean} and {self.pixel_std} have different shapes!"

        self.output_dir = output_dir

        self.dataset_names = dataset_names
        from detectron2.data.catalog import MetadataCatalog

        if isinstance(dataset_metas, str):
            dataset_metas = [
                dataset_metas,
            ]
        self.metadata_list = [copy.deepcopy(MetadataCatalog.get(d)) for d in dataset_metas]

        self.dataset_prompts = dataset_prompts
        self.dataset_entities = []
        for i, metadata in enumerate(self.metadata_list):
            if "stuffonly" in metadata.name:
                del metadata.thing_classes

            if (
                metadata.get("thing_classes", None) is not None
                and metadata.get("stuff_classes", None) is not None
            ):
                self.dataset_entities.append("thing+stuff")
            elif metadata.get("thing_classes", None) is not None:
                self.dataset_entities.append("thing")
            elif metadata.get("stuff_classes", None) is not None:
                self.dataset_entities.append("stuff")
            else:
                self.dataset_entities.append("thing")

            logger.info("dataset_id: " + str(i))
            logger.info("dataset_name: " + metadata.name)
            logger.info("thing_classes: " + str(metadata.get("thing_classes", None)))
            logger.info("stuff_classes: " + str(metadata.get("stuff_classes", None)))
            logger.info("dataset_entity: " + self.dataset_entities[i])

        self.dataset_name_to_idx = {k: i for i, k in enumerate(self.dataset_names)}
        self.dataset_name_to_entity = {
            k: i for i, k in zip(self.dataset_entities, self.dataset_names)
        }

        self.eval_dataset_id = -1
        self.eval_dataset_entity = ""

        self.text_feature_bank = text_feature_bank
        self.text_feature_bank_reset = text_feature_bank_reset
        self.text_feature_bank_random_size = text_feature_bank_random_size
        if self.text_feature_bank:
            features_phrase_bank = torch.zeros(
                (
                    len(self.criterion),
                    max([ctr.num_classes for ctr in self.criterion]),
                    embed_dim_language,
                ),
                dtype=torch.float,
                device=self.device,
            )
            self.register_buffer("features_phrase_bank", features_phrase_bank, False)

        self.text_feature_reduce_type = text_feature_reduce_type
        self.text_feature_reduce_before_fusion = text_feature_reduce_before_fusion
        self.expression_cumulative_gt_class = expression_cumulative_gt_class
        self.embed_dim_language = embed_dim_language

    @property
    def device(self):
        return self.pixel_mean.device

    def _move_to_current_device(self, x):
        return move_device_like(x, self.pixel_mean)

    def forward(self, batched_inputs, do_postprocess=True):
        images = self.preprocess_image(batched_inputs)

        batch_size, _, H, W = images.tensor.shape
        img_masks = images.tensor.new_ones(batch_size, H, W)
        for image_id, image_size in enumerate(images.image_sizes):
            img_masks[image_id, : image_size[0], : image_size[1]] = 0

        features = self.backbone(images.tensor)  # output feature dict

        if self.neck is not None:
            multi_level_feats = self.neck({f: features[f] for f in self.neck.in_features})
        else:
            multi_level_feats = [feat for feat_name, feat in features.items()]
        multi_level_masks = []
        multi_level_position_embeddings = []
        for feat in multi_level_feats:
            multi_level_masks.append(
                F.interpolate(img_masks[None], size=feat.shape[-2:]).to(torch.bool).squeeze(0)
            )
            multi_level_position_embeddings.append(
                self.position_embedding(multi_level_masks[-1]).to(images.tensor.dtype)
            )

        query_embeds = None
        if not self.as_two_stage:
            query_embeds = self.query_embedding.weight

        (
            inter_states,
            init_reference,
            inter_references,
            enc_outputs_class,
            enc_outputs_coord_unact,
            anchors,
            memory,
        ) = self.transformer(
            multi_level_feats, multi_level_masks, multi_level_position_embeddings, query_embeds
        )

        outputs_classes = []
        outputs_coords = []
        for lvl in range(inter_states.shape[0]):
            if lvl == 0:
                reference = init_reference
            else:
                reference = inter_references[lvl - 1]
            reference = inverse_sigmoid(reference)
            outputs_class = self.class_embed[lvl](inter_states[lvl])
            tmp = self.bbox_embed[lvl](inter_states[lvl])
            if reference.shape[-1] == 4:
                tmp += reference
            else:
                assert reference.shape[-1] == 2
                tmp[..., :2] += reference
            outputs_coord = tmp.sigmoid()
            outputs_classes.append(outputs_class)
            outputs_coords.append(outputs_coord)
        outputs_class = torch.stack(outputs_classes)
        outputs_coord = torch.stack(outputs_coords)

        output = {
            "pred_logits": outputs_class[-1],
            "pred_boxes": outputs_coord[-1],
            "init_reference": init_reference,
        }
        if self.aux_loss:
            output["aux_outputs"] = self._set_aux_loss(outputs_class, outputs_coord)

        if self.as_two_stage:
            enc_outputs_coord = enc_outputs_coord_unact.sigmoid()
            output["enc_outputs"] = {
                "pred_logits": enc_outputs_class,
                "pred_boxes": enc_outputs_coord,
                "anchors": anchors,
            }

        if self.training:
            gt_instances = [x["instances"].to(self.device) for x in batched_inputs]
            targets = self.prepare_targets(gt_instances)
            loss_dict = self.criterion(output, targets)
            weight_dict = self.criterion.weight_dict
            for k in loss_dict.keys():
                if k in weight_dict:
                    loss_dict[k] *= weight_dict[k]
            return loss_dict
        else:
            del features
            del multi_level_feats

            box_cls = output["pred_logits"]
            box_pred = output["pred_boxes"]
            results, filter_inds = self.inference(box_cls, box_pred, images.image_sizes)

            if do_postprocess:
                assert not torch.jit.is_scripting(), "Scripting is not supported for postprocess."
                return GeneralizedRCNN._postprocess(results, batched_inputs, images.image_sizes)
            return results

    @torch.jit.unused
    def _set_aux_loss(self, outputs_class, outputs_coord):
        return [
            {"pred_logits": a, "pred_boxes": b}
            for a, b in zip(outputs_class[:-1], outputs_coord[:-1])
        ]

    def inference(self, box_cls, box_pred, image_sizes):
        """
        Arguments:
            box_cls (Tensor): tensor of shape (batch_size, num_queries, K).
                The tensor predicts the classification probability for each query.
            box_pred (Tensor): tensors of shape (batch_size, num_queries, 4).
                The tensor predicts 4-vector (x,y,w,h) box
                regression values for every queryx
            image_sizes (List[torch.Size]): the input image sizes

        Returns:
            results (List[Instances]): a list of #images elements.
        """

        if True:
            return NMSPostProcess()(
                {"pred_logits": box_cls, "pred_boxes": box_pred},
                torch.tensor([list(x) for x in image_sizes], device=self.device),
                self.select_box_nums_for_evaluation,
            )

            scores = torch.cat(
                (
                    box_cls.sigmoid(),
                    torch.zeros((box_cls.size(0), box_cls.size(1), 1), device=self.device),
                ),
                dim=2,
            )

            boxes = box_cxcywh_to_xyxy(box_pred)

            img_h, img_w = torch.tensor(image_sizes, device=self.device).unbind(1)
            scale_fct = torch.stack([img_w, img_h, img_w, img_h], dim=1)
            boxes = boxes * scale_fct[:, None, :]

            boxes = boxes.unbind(0)
            scores = scores.unbind(0)
            image_shapes = image_sizes

            self.test_topk_per_image = self.select_box_nums_for_evaluation
            self.test_nms_thresh = 0.7
            self.test_score_thresh = 0.05

            return fast_rcnn_inference(
                boxes,
                scores,
                image_shapes,
                self.test_score_thresh,
                self.test_nms_thresh,
                self.test_topk_per_image,
            )

        assert len(box_cls) == len(image_sizes)
        results = []

        prob = box_cls.sigmoid()
        topk_values, topk_indexes = torch.topk(
            prob.view(box_cls.shape[0], -1), self.select_box_nums_for_evaluation, dim=1
        )
        scores = topk_values
        topk_boxes = torch.div(topk_indexes, box_cls.shape[2], rounding_mode="floor")
        labels = topk_indexes % box_cls.shape[2]

        boxes = torch.gather(box_pred, 1, topk_boxes.unsqueeze(-1).repeat(1, 1, 4))

        for i, (scores_per_image, labels_per_image, box_pred_per_image, image_size) in enumerate(
            zip(scores, labels, boxes, image_sizes)
        ):
            result = Instances(image_size)
            result.pred_boxes = Boxes(box_cxcywh_to_xyxy(box_pred_per_image))
            result.pred_boxes.scale(scale_x=image_size[1], scale_y=image_size[0])
            result.scores = scores_per_image
            result.pred_classes = labels_per_image
            results.append(result)
        return results, topk_indexes

    def prepare_targets(self, targets):
        new_targets = []
        for targets_per_image in targets:
            h, w = targets_per_image.image_size
            image_size_xyxy = torch.as_tensor([w, h, w, h], dtype=torch.float, device=self.device)
            gt_classes = targets_per_image.gt_classes
            gt_boxes = targets_per_image.gt_boxes.tensor / image_size_xyxy
            gt_boxes = box_xyxy_to_cxcywh(gt_boxes)
            new_targets.append({"labels": gt_classes, "boxes": gt_boxes})
        return new_targets

    def preprocess_image(self, batched_inputs):
        images = [self._move_to_current_device(x["image"]) for x in batched_inputs]
        images = [x.to(self.pixel_mean.dtype) for x in images]
        images = [(x - self.pixel_mean) / self.pixel_std for x in images]
        images = ImageList.from_tensors(
            images,
            self.backbone.size_divisibility,
            padding_constraints=self.backbone.padding_constraints,
        )
        return images

    @staticmethod
    def _postprocess(instances, batched_inputs: List[Dict[str, torch.Tensor]], image_sizes):
        """
        Rescale the output instances to the target size.
        """
        processed_results = []
        for results_per_image, input_per_image, image_size in zip(
            instances, batched_inputs, image_sizes
        ):
            height = input_per_image.get("height", image_size[0])
            width = input_per_image.get("width", image_size[1])
            r = detector_postprocess(results_per_image, height, width)
            processed_results.append({"instances": r})
        return processed_results

    def set_eval_dataset(self, dataset_name):
        for d in self.dataset_names:
            if sum([dd in dataset_name for dd in d.split("+")]):
                self.eval_dataset_id = self.dataset_name_to_idx[d]
                self.eval_dataset_entity = self.dataset_name_to_entity[d]
                break
        else:
            self.eval_dataset_id = -1
            self.eval_dataset_entity = ""

        logger.info(
            "Setting eval dataset to: "
            + str(d)
            + " "
            + str(dataset_name)
            + " "
            + str(self.eval_dataset_id)
        )
        logger.info(
            "Setting eval entity to: "
            + str(d)
            + " "
            + str(dataset_name)
            + " "
            + str(self.eval_dataset_entity)
        )


class NMSPostProcess(nn.Module):
    """This module converts the model's output into the format expected by the coco api"""

    @torch.no_grad()
    def forward(self, outputs, target_sizes, select_box_nums_for_evaluation):
        """Perform the computation
        Parameters:
            outputs: raw outputs of the model
            target_sizes: tensor of dimension [batch_size x 2] containing the size of each images of the batch
                          For evaluation, this must be the original image size (before any data augmentation)
                          For visualization, this should be the image size after data augment, but before padding
        """
        out_logits, out_bbox = outputs["pred_logits"], outputs["pred_boxes"]
        bs, n_queries, n_cls = out_logits.shape

        assert len(out_logits) == len(target_sizes)
        assert target_sizes.shape[1] == 2

        prob = out_logits.sigmoid()

        all_scores = prob.view(bs, n_queries * n_cls).to(out_logits.device)
        all_indexes = torch.arange(n_queries * n_cls)[None].repeat(bs, 1).to(out_logits.device)
        all_boxes = torch.div(all_indexes, out_logits.shape[2], rounding_mode="trunc")
        all_labels = all_indexes % out_logits.shape[2]

        boxes = box_cxcywh_to_xyxy(out_bbox)
        boxes = torch.gather(boxes, 1, all_boxes.unsqueeze(-1).repeat(1, 1, 4))

        img_h, img_w = target_sizes.unbind(1)
        scale_fct = torch.stack([img_w, img_h, img_w, img_h], dim=1)
        boxes = boxes * scale_fct[:, None, :]

        results = []
        keep_inds_all = []
        for b in range(bs):
            box = boxes[b]
            score = all_scores[b]
            lbls = all_labels[b]

            pre_topk = score.topk(10000).indices
            box = box[pre_topk]
            score = score[pre_topk]
            lbls = lbls[pre_topk]

            keep_inds = batched_nms(box, score, lbls, 0.7)[:select_box_nums_for_evaluation]

            result = Instances(target_sizes[b])
            result.pred_boxes = Boxes(box[keep_inds])
            result.scores = score[keep_inds]
            result.pred_classes = lbls[keep_inds]
            results.append(result)

            keep_inds_all.append(keep_inds)

        return results, keep_inds_all
