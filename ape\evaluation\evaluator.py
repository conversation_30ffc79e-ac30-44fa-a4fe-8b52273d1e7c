# Copyright (c) Facebook, Inc. and its affiliates.
import datetime
import logging
import time
from collections import abc
from contextlib import ExitStack
from typing import List, Union

import torch
from torch import nn

from detectron2.evaluation import DatasetEvaluator, DatasetEvaluators, inference_context
from detectron2.utils.comm import get_world_size
from detectron2.utils.logger import log_every_n_seconds


def inference_on_dataset(
    model, data_loader, evaluator: Union[DatasetEvaluator, List[DatasetEvaluator], None]
):
    """
    Run model on the data_loader and evaluate the metrics with evaluator.
    Also benchmark the inference speed of `model.__call__` accurately.
    The model will be used in eval mode.

    Args:
        model (callable): a callable which takes an object from
            `data_loader` and returns some outputs.

            If it's an nn.Module, it will be temporarily set to `eval` mode.
            If you wish to evaluate a model in `training` mode instead, you can
            wrap the given model and override its behavior of `.eval()` and `.train()`.
        data_loader: an iterable object with a length.
            The elements it generates will be the inputs to the model.
        evaluator: the evaluator(s) to run. Use `None` if you only want to benchmark,
            but don't want to do any evaluation.

    Returns:
        The return value of `evaluator.evaluate()`
    """
    num_devices = get_world_size()
    logger = logging.getLogger(__name__)
    logger.info("Start inference on {} batches".format(len(data_loader)))

    total = len(data_loader)  # inference data loader must have a fixed length
    if evaluator is None:
        # create a no-op evaluator
        evaluator = DatasetEvaluators([])
    if isinstance(evaluator, abc.MutableSequence):
        evaluator = DatasetEvaluators(evaluator)
    evaluator.reset()

    num_warmup = min(5, total - 1)
    start_time = time.perf_counter()
    total_data_time = 0
    total_compute_time = 0
    total_eval_time = 0

    total_preprocess_time = 0
    total_backbone_time = 0
    total_transformer_time = 0
    total_postprocess_time = 0

    with ExitStack() as stack:
        if isinstance(model, nn.Module):
            stack.enter_context(inference_context(model))
        stack.enter_context(torch.no_grad())

        start_data_time = time.perf_counter()
        for idx, inputs in enumerate(data_loader):
            total_data_time += time.perf_counter() - start_data_time
            if idx == num_warmup:
                start_time = time.perf_counter()
                total_data_time = 0
                total_compute_time = 0
                total_eval_time = 0

                total_preprocess_time = 0
                total_backbone_time = 0
                total_transformer_time = 0
                total_postprocess_time = 0

            start_compute_time = time.perf_counter()
            outputs = model(inputs)
            if torch.cuda.is_available():
                torch.cuda.synchronize()
            total_compute_time += time.perf_counter() - start_compute_time

            start_eval_time = time.perf_counter()
            evaluator.process(inputs, outputs)
            total_eval_time += time.perf_counter() - start_eval_time

            if hasattr(model.module, "preprocess_time"):
                total_preprocess_time += model.module.preprocess_time
            if hasattr(model.module, "model_vision") and hasattr(
                model.module.model_vision, "preprocess_time"
            ):
                total_preprocess_time += model.module.model_vision.preprocess_time
            if hasattr(model.module, "backbone_time"):
                total_backbone_time += model.module.backbone_time
            if hasattr(model.module, "model_vision") and hasattr(
                model.module.model_vision, "backbone_time"
            ):
                total_backbone_time += model.module.model_vision.backbone_time
            if hasattr(model.module, "transformer_time"):
                total_transformer_time += model.module.transformer_time
            if hasattr(model.module, "model_vision") and hasattr(
                model.module.model_vision, "transformer_time"
            ):
                total_transformer_time += model.module.model_vision.transformer_time
            if hasattr(model.module, "postprocess_time"):
                total_postprocess_time += model.module.postprocess_time
            if hasattr(model.module, "model_vision") and hasattr(
                model.module.model_vision, "postprocess_time"
            ):
                total_postprocess_time += model.module.model_vision.postprocess_time

            iters_after_start = idx + 1 - num_warmup * int(idx >= num_warmup)
            data_seconds_per_iter = total_data_time / iters_after_start
            compute_seconds_per_iter = total_compute_time / iters_after_start
            eval_seconds_per_iter = total_eval_time / iters_after_start
            total_seconds_per_iter = (time.perf_counter() - start_time) / iters_after_start

            preprocess_seconds_per_iter = total_preprocess_time / iters_after_start
            backbone_seconds_per_iter = total_backbone_time / iters_after_start
            transformer_seconds_per_iter = total_transformer_time / iters_after_start
            postprocess_seconds_per_iter = total_postprocess_time / iters_after_start

            if idx >= num_warmup * 2 or compute_seconds_per_iter > 5:
                eta = datetime.timedelta(seconds=int(total_seconds_per_iter * (total - idx - 1)))
                if torch.cuda.is_available():
                    max_mem_mb = torch.cuda.max_memory_allocated() / 1024.0 / 1024.0
                else:
                    max_mem_mb = 0
                log_every_n_seconds(
                    logging.INFO,
                    (
                        f"Inference done {idx + 1}/{total}. "
                        f"Dataloading: {data_seconds_per_iter:.4f} s/iter. "
                        f"Inference: {compute_seconds_per_iter:.4f} s/iter. "
                        f"Eval: {eval_seconds_per_iter:.4f} s/iter. "
                        f"Total: {total_seconds_per_iter:.4f} s/iter. "
                        f"ETA={eta}"
                        f". "
                        f"preprocess: {preprocess_seconds_per_iter:.4f} s/iter. "
                        f"backbone: {backbone_seconds_per_iter:.4f} s/iter. "
                        f"transformer: {transformer_seconds_per_iter:.4f} s/iter. "
                        f"postprocess: {postprocess_seconds_per_iter:.4f} s/iter. "
                        f"max_mem: {max_mem_mb:.0f}M. "
                    ),
                    n=5,
                )
            if idx < num_warmup * 2:
                torch.cuda.reset_peak_memory_stats()
            start_data_time = time.perf_counter()

    # Measure the time only for this worker (before the synchronization barrier)
    total_time = time.perf_counter() - start_time
    total_time_str = str(datetime.timedelta(seconds=total_time))
    # NOTE this format is parsed by grep
    logger.info(
        "Total inference time: {} ({:.6f} s / iter per device, on {} devices)".format(
            total_time_str, total_time / (total - num_warmup), num_devices
        )
    )
    total_compute_time_str = str(datetime.timedelta(seconds=int(total_compute_time)))
    logger.info(
        "Total inference pure compute time: {} ({:.6f} s / iter per device, on {} devices)".format(
            total_compute_time_str, total_compute_time / (total - num_warmup), num_devices
        )
    )

    results = evaluator.evaluate()
    # An evaluator may return None when not in main process.
    # Replace it by an empty dict instead to make it easier for downstream code to handle
    if results is None:
        results = {}
    return results
